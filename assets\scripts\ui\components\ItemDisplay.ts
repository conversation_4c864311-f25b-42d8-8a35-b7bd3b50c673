import { _decorator, Component, Node, Sprite, Label, Button, SpriteFrame } from 'cc';
import { ItemData } from '../../data/ItemData';
import { DataManager } from '../../managers/DataManager';
import { EventManager, GameEvents, ItemEventData } from '../../managers/EventManager';
const { ccclass, property } = _decorator;

/**
 * 道具显示组件
 * 用于显示道具图标、名称、数量等信息
 */
@ccclass('ItemDisplay')
export class ItemDisplay extends Component {
    
    @property({ type: Sprite, tooltip: "道具图标" })
    public itemIcon: Sprite = null;
    
    @property({ type: Label, tooltip: "道具名称" })
    public itemNameLabel: Label = null;
    
    @property({ type: Label, tooltip: "道具数量" })
    public itemCountLabel: Label = null;
    
    @property({ type: Label, tooltip: "道具描述" })
    public itemDescLabel: Label = null;
    
    @property({ type: Button, tooltip: "道具按钮" })
    public itemButton: Button = null;
    
    @property({ type: Node, tooltip: "数量背景" })
    public countBackground: Node = null;
    
    @property({ tooltip: "是否显示数量" })
    public showCount: boolean = true;
    
    @property({ tooltip: "是否显示描述" })
    public showDescription: boolean = false;
    
    @property({ tooltip: "是否可点击" })
    public clickable: boolean = true;

    private _itemData: ItemData = null;
    private _itemCount: number = 0;
    private _onClickCallback: Function = null;

    onLoad() {
        this.setupButton();
        this.updateDisplay();
    }

    /**
     * 设置按钮事件
     */
    private setupButton() {
        if (this.itemButton && this.clickable) {
            this.itemButton.node.on(Button.EventType.CLICK, this.onItemClick, this);
        }
    }

    /**
     * 设置道具数据
     */
    public setItemData(itemData: ItemData, count: number = 1) {
        this._itemData = itemData;
        this._itemCount = count;
        this.updateDisplay();
    }

    /**
     * 设置道具ID
     */
    public setItemById(itemId: string, count: number = 1) {
        const itemData = DataManager.instance.getItemById(itemId);
        if (itemData) {
            this.setItemData(itemData, count);
        }
    }

    /**
     * 更新显示
     */
    private updateDisplay() {
        if (!this._itemData) {
            this.clearDisplay();
            return;
        }
        
        // 更新图标
        this.updateIcon();
        
        // 更新名称
        this.updateName();
        
        // 更新数量
        this.updateCount();
        
        // 更新描述
        this.updateDescription();
    }

    /**
     * 更新图标
     */
    private updateIcon() {
        if (this.itemIcon && this._itemData) {
            // 这里应该加载道具图标
            // 暂时使用默认图标
            console.log(`加载道具图标: ${this._itemData.iconPath}`);
        }
    }

    /**
     * 更新名称
     */
    private updateName() {
        if (this.itemNameLabel && this._itemData) {
            this.itemNameLabel.string = this._itemData.name;
        }
    }

    /**
     * 更新数量
     */
    private updateCount() {
        if (this.itemCountLabel) {
            if (this.showCount && this._itemCount > 1) {
                this.itemCountLabel.string = this._itemCount.toString();
                this.itemCountLabel.node.active = true;
                if (this.countBackground) {
                    this.countBackground.active = true;
                }
            } else {
                this.itemCountLabel.node.active = false;
                if (this.countBackground) {
                    this.countBackground.active = false;
                }
            }
        }
    }

    /**
     * 更新描述
     */
    private updateDescription() {
        if (this.itemDescLabel) {
            if (this.showDescription && this._itemData) {
                this.itemDescLabel.string = this._itemData.description;
                this.itemDescLabel.node.active = true;
            } else {
                this.itemDescLabel.node.active = false;
            }
        }
    }

    /**
     * 清除显示
     */
    private clearDisplay() {
        if (this.itemIcon) {
            this.itemIcon.spriteFrame = null;
        }
        
        if (this.itemNameLabel) {
            this.itemNameLabel.string = "";
        }
        
        if (this.itemCountLabel) {
            this.itemCountLabel.node.active = false;
        }
        
        if (this.itemDescLabel) {
            this.itemDescLabel.node.active = false;
        }
        
        if (this.countBackground) {
            this.countBackground.active = false;
        }
    }

    /**
     * 道具点击事件
     */
    private onItemClick() {
        if (!this._itemData) return;
        
        // 发送道具点击事件
        const eventData: ItemEventData = {
            itemId: this._itemData.id,
            itemData: this._itemData,
            count: this._itemCount
        };
        
        EventManager.instance.emit(GameEvents.ITEM_USE, eventData);
        
        // 执行回调
        if (this._onClickCallback) {
            this._onClickCallback(this._itemData, this._itemCount);
        }
    }

    /**
     * 设置点击回调
     */
    public setClickCallback(callback: Function) {
        this._onClickCallback = callback;
    }

    /**
     * 设置数量
     */
    public setCount(count: number) {
        this._itemCount = count;
        this.updateCount();
    }

    /**
     * 增加数量
     */
    public addCount(amount: number) {
        this._itemCount += amount;
        this.updateCount();
    }

    /**
     * 减少数量
     */
    public reduceCount(amount: number) {
        this._itemCount = Math.max(0, this._itemCount - amount);
        this.updateCount();
        
        // 如果数量为0，可以隐藏或移除
        if (this._itemCount === 0) {
            this.onCountZero();
        }
    }

    /**
     * 数量为0时的处理
     */
    private onCountZero() {
        // 可以在这里添加特殊处理，比如隐藏节点或播放动画
        console.log(`道具 ${this._itemData?.name} 数量为0`);
    }

    /**
     * 设置可见性
     */
    public setVisible(visible: boolean) {
        this.node.active = visible;
    }

    /**
     * 设置可交互性
     */
    public setInteractable(interactable: boolean) {
        if (this.itemButton) {
            this.itemButton.interactable = interactable;
        }
    }

    /**
     * 获取道具数据
     */
    public get itemData(): ItemData {
        return this._itemData;
    }

    /**
     * 获取道具数量
     */
    public get itemCount(): number {
        return this._itemCount;
    }

    /**
     * 检查是否有道具
     */
    public hasItem(): boolean {
        return this._itemData !== null && this._itemCount > 0;
    }

    /**
     * 检查是否是指定道具
     */
    public isItem(itemId: string): boolean {
        return this._itemData?.id === itemId;
    }

    /**
     * 播放使用动画
     */
    public playUseAnimation() {
        // 这里可以添加道具使用时的动画效果
        console.log(`播放道具使用动画: ${this._itemData?.name}`);
    }

    /**
     * 播放获得动画
     */
    public playObtainAnimation() {
        // 这里可以添加获得道具时的动画效果
        console.log(`播放道具获得动画: ${this._itemData?.name}`);
    }

    onDestroy() {
        // 清理按钮事件
        if (this.itemButton) {
            this.itemButton.node.off(Button.EventType.CLICK, this.onItemClick, this);
        }
    }
}
