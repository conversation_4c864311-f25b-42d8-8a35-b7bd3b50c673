import { _decorator, Component, Node, Button, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

/**
 * UI面板基类
 * 所有UI面板都应该继承此类
 */
@ccclass('BasePanel')
export class BasePanel extends Component {
    
    @property({ type: Button, tooltip: "关闭按钮" })
    public closeButton: Button = null;
    
    @property({ type: Node, tooltip: "面板内容节点" })
    public contentNode: Node = null;
    
    @property({ tooltip: "是否显示动画" })
    public showAnimation: boolean = true;
    
    @property({ tooltip: "动画持续时间" })
    public animationDuration: number = 0.3;

    protected _isInitialized: boolean = false;
    protected _panelData: any = null;

    onLoad() {
        this.setupCloseButton();
        this.setupContent();
    }

    start() {
        if (this.showAnimation) {
            this.playShowAnimation();
        }
    }

    /**
     * 设置关闭按钮
     */
    private setupCloseButton() {
        if (this.closeButton) {
            this.closeButton.node.on(Button.EventType.CLICK, this.onCloseButtonClick, this);
        }
    }

    /**
     * 设置内容节点
     */
    private setupContent() {
        if (!this.contentNode) {
            this.contentNode = this.node;
        }
    }

    /**
     * 初始化面板
     */
    public init(data?: any) {
        this._panelData = data;
        this.onInit(data);
        this._isInitialized = true;
    }

    /**
     * 子类重写此方法进行初始化
     */
    protected onInit(data?: any) {
        // 子类重写
    }

    /**
     * 显示面板
     */
    public show() {
        this.node.active = true;
        if (this.showAnimation) {
            this.playShowAnimation();
        }
        this.onShow();
    }

    /**
     * 隐藏面板
     */
    public hide() {
        if (this.showAnimation) {
            this.playHideAnimation(() => {
                this.node.active = false;
                this.onHide();
            });
        } else {
            this.node.active = false;
            this.onHide();
        }
    }

    /**
     * 关闭面板
     */
    public close() {
        this.onClose();
        if (this.showAnimation) {
            this.playHideAnimation(() => {
                this.node.destroy();
            });
        } else {
            this.node.destroy();
        }
    }

    /**
     * 播放显示动画
     */
    private playShowAnimation() {
        if (!this.contentNode) return;
        
        // 初始状态：缩放为0
        this.contentNode.setScale(0, 0, 1);
        
        // 动画到正常大小
        tween(this.contentNode)
            .to(this.animationDuration, { scale: new Vec3(1, 1, 1) }, {
                easing: 'backOut'
            })
            .start();
    }

    /**
     * 播放隐藏动画
     */
    private playHideAnimation(callback?: Function) {
        if (!this.contentNode) {
            if (callback) callback();
            return;
        }
        
        // 动画到缩放为0
        tween(this.contentNode)
            .to(this.animationDuration, { scale: new Vec3(0, 0, 1) }, {
                easing: 'backIn'
            })
            .call(() => {
                if (callback) callback();
            })
            .start();
    }

    /**
     * 关闭按钮点击事件
     */
    private onCloseButtonClick() {
        this.close();
    }

    /**
     * 面板显示时调用
     */
    protected onShow() {
        // 子类重写
    }

    /**
     * 面板隐藏时调用
     */
    protected onHide() {
        // 子类重写
    }

    /**
     * 面板关闭时调用
     */
    protected onClose() {
        // 子类重写
    }

    /**
     * 获取面板数据
     */
    protected getPanelData(): any {
        return this._panelData;
    }

    /**
     * 检查是否已初始化
     */
    public get isInitialized(): boolean {
        return this._isInitialized;
    }

    onDestroy() {
        // 清理事件监听
        if (this.closeButton) {
            this.closeButton.node.off(Button.EventType.CLICK, this.onCloseButtonClick, this);
        }
    }
}
