import { _decorator, Component, Node, Label, ProgressBar, tween, Vec3 } from 'cc';
import { BasePanel } from '../BasePanel.ts';
const { ccclass, property } = _decorator;

/**
 * 加载界面组件
 * 用于显示游戏加载进度和状态
 */
@ccclass('LoadingPanel')
export class LoadingPanel extends BasePanel {
    
    @property({ type: Label, tooltip: "加载提示文本" })
    public loadingLabel: Label = null;
    
    @property({ type: ProgressBar, tooltip: "进度条" })
    public progressBar: ProgressBar = null;
    
    @property({ type: Node, tooltip: "加载动画节点" })
    public loadingAnimation: Node = null;
    
    @property({ type: Label, tooltip: "进度百分比标签" })
    public percentLabel: Label = null;
    
    @property({ tooltip: "是否显示进度条" })
    public showProgress: boolean = true;
    
    @property({ tooltip: "是否显示百分比" })
    public showPercent: boolean = true;

    private _currentProgress: number = 0;
    private _targetProgress: number = 0;
    private _loadingTexts: string[] = [
        "正在加载游戏资源...",
        "正在初始化动物数据...",
        "正在准备收容所...",
        "正在连接动物朋友们...",
        "马上就好了..."
    ];
    private _currentTextIndex: number = 0;
    private _textChangeTimer: number = 0;

    onLoad() {
        super.onLoad();
        this.showAnimation = false; // 加载界面不需要显示动画
        this.setupUI();
        this.startLoadingAnimation();
    }

    start() {
        // 不调用父类的start，避免播放显示动画
        this.startTextRotation();
    }

    /**
     * 设置UI显示
     */
    private setupUI() {
        if (this.progressBar) {
            this.progressBar.node.active = this.showProgress;
            this.progressBar.progress = 0;
        }
        
        if (this.percentLabel) {
            this.percentLabel.node.active = this.showPercent;
            this.percentLabel.string = "0%";
        }
        
        if (this.loadingLabel) {
            this.loadingLabel.string = this._loadingTexts[0];
        }
    }

    /**
     * 开始加载动画
     */
    private startLoadingAnimation() {
        if (!this.loadingAnimation) return;
        
        // 旋转动画
        tween(this.loadingAnimation)
            .repeatForever(
                tween()
                    .by(1, { angle: 360 })
            )
            .start();
    }

    /**
     * 开始文本轮换
     */
    private startTextRotation() {
        this.schedule(this.changeLoadingText, 2); // 每2秒切换一次文本
    }

    /**
     * 切换加载文本
     */
    private changeLoadingText() {
        if (!this.loadingLabel) return;
        
        this._currentTextIndex = (this._currentTextIndex + 1) % this._loadingTexts.length;
        this.loadingLabel.string = this._loadingTexts[this._currentTextIndex];
    }

    /**
     * 设置加载进度
     */
    public setProgress(progress: number, text?: string) {
        this._targetProgress = Math.max(0, Math.min(1, progress));
        
        if (text && this.loadingLabel) {
            this.loadingLabel.string = text;
        }
        
        this.updateProgress();
    }

    /**
     * 更新进度显示
     */
    private updateProgress() {
        if (this.progressBar) {
            // 平滑过渡到目标进度
            tween(this.progressBar)
                .to(0.3, { progress: this._targetProgress })
                .call(() => {
                    this._currentProgress = this._targetProgress;
                })
                .start();
        }
        
        if (this.percentLabel) {
            const percent = Math.floor(this._targetProgress * 100);
            this.percentLabel.string = `${percent}%`;
        }
    }

    /**
     * 设置加载文本
     */
    public setLoadingText(text: string) {
        if (this.loadingLabel) {
            this.loadingLabel.string = text;
        }
    }

    /**
     * 添加自定义加载文本
     */
    public addLoadingText(text: string) {
        this._loadingTexts.push(text);
    }

    /**
     * 完成加载
     */
    public completeLoading(callback?: Function) {
        this.setProgress(1, "加载完成！");
        
        // 延迟一下再执行回调，让用户看到100%
        this.scheduleOnce(() => {
            if (callback) {
                callback();
            }
            this.close();
        }, 0.5);
    }

    /**
     * 显示加载错误
     */
    public showError(errorMessage: string, onRetry?: Function) {
        if (this.loadingLabel) {
            this.loadingLabel.string = `加载失败: ${errorMessage}`;
        }
        
        // 停止动画
        if (this.loadingAnimation) {
            this.loadingAnimation.stopAllActions();
        }
        
        // 这里可以显示重试按钮
        console.log("加载错误:", errorMessage);
    }

    /**
     * 静态方法：显示加载界面
     */
    public static show(text?: string, showProgress: boolean = true): LoadingPanel {
        // 这里应该通过UIManager来显示
        console.log(`显示加载界面: ${text || "加载中..."}`);
        return null; // 实际实现中应该返回LoadingPanel实例
    }

    /**
     * 静态方法：隐藏加载界面
     */
    public static hide() {
        // 这里应该通过UIManager来隐藏
        console.log("隐藏加载界面");
    }

    /**
     * 静态方法：更新加载进度
     */
    public static updateProgress(progress: number, text?: string) {
        // 这里应该更新当前显示的加载界面
        console.log(`更新加载进度: ${Math.floor(progress * 100)}% ${text || ""}`);
    }

    onDestroy() {
        super.onDestroy();
        
        // 停止所有定时器
        this.unscheduleAllCallbacks();
        
        // 停止动画
        if (this.loadingAnimation) {
            this.loadingAnimation.stopAllActions();
        }
    }
}
