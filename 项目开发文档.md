# 动物收容所游戏开发文档

## 项目概述
基于Cocos Creator 3.8.6开发的2D竖屏手机游戏，支持微信小游戏、iOS和Android平台。

## 项目结构

### 核心架构
```
assets/
├── scripts/
│   ├── GameManager.ts              # 游戏主管理器
│   ├── managers/                   # 管理器模块
│   │   ├── DataManager.ts          # 数据管理器
│   │   ├── UIManager.ts            # UI管理器
│   │   └── SceneManager.ts         # 场景管理器
│   ├── ui/                         # UI组件
│   │   └── BasePanel.ts            # UI面板基类
│   ├── scenes/                     # 场景脚本
│   │   └── MainScene.ts            # 主场景控制器
│   └── config/                     # 配置文件
│       └── GameConfig.ts           # 游戏配置
├── scenes/                         # 场景文件
├── prefabs/                        # 预制体
├── textures/                       # 贴图资源
├── audio/                          # 音频资源
└── data/                           # 数据文件
```

## 核心系统设计

### 1. 数据管理系统 (DataManager)
- **功能**: 管理玩家数据、动物数据、道具数据
- **存储**: 使用localStorage进行本地存储
- **Mock数据**: 提供完整的Mock数据系统，便于开发测试

#### 数据结构
```typescript
// 玩家数据
interface PlayerData {
    playerId: string;           // 玩家ID
    playerName: string;         // 玩家名称
    level: number;              // 等级
    experience: number;         // 经验值
    coins: number;              // 金币
    gems: number;               // 宝石
    items: string[];            // 拥有的道具ID列表
    ownedAnimals: string[];     // 拥有的动物ID列表
    travelingAnimals: string[]; // 旅行中的动物ID列表
    lastLoginTime: number;      // 最后登录时间
    totalPlayTime: number;      // 总游戏时间
    achievements: string[];     // 已获得成就列表
}

// 动物数据
interface AnimalData {
    id: string;                 // 动物ID
    name: string;               // 动物名称
    type: string;               // 动物类型
    rarity: string;             // 稀有度
    description: string;        // 描述
    iconPath: string;           // 图标路径
    personality: string;        // 性格
    favoriteFood: string[];     // 喜欢的食物
    specialAbility: string;     // 特殊能力
}

// 道具数据
interface ItemData {
    id: string;                 // 道具ID
    name: string;               // 道具名称
    type: string;               // 道具类型
    rarity: string;             // 稀有度
    description: string;        // 描述
    iconPath: string;           // 图标路径
    price: number;              // 价格
    effect: string;             // 效果描述
}
```

### 2. UI管理系统 (UIManager)
- **功能**: 管理UI层级、面板显示隐藏、动画效果
- **特点**: 所有界面都是图层形式，支持返回操作
- **层级**: Background → Scene → UI → Popup → Loading → Top

### 3. 场景管理系统 (SceneManager)
- **功能**: 场景切换、预加载、历史记录
- **场景列表**:
  - MainScene: 主场景
  - HouseScene: 主屋场景
  - BackyardScene: 后院场景
  - ShopScene: 商店场景
  - WeChatScene: 微信场景

## 游戏功能模块

### 1. 主屋系统
- **抽屉管理**: 道具存储和管理
- **道具使用**: 各种道具的使用逻辑
- **基础交互**: 与环境的交互

### 2. 后院系统
- **动物展示**: 显示当前在家的动物
- **动物互动**: 与动物的各种互动
- **动物管理**: 动物的基本管理功能

### 3. 动物来访系统
- **随机来访**: 动物随机敲门来访
- **收养决策**: 玩家决定是否收养
- **前门交互**: 前门的各种交互逻辑

### 4. 旅行系统
- **外出旅行**: 动物从后门离开去旅行
- **虚拟微信**: 模拟微信聊天界面
- **照片分享**: 动物发送旅行照片
- **礼物系统**: 动物带回礼物和金币

### 5. 道具商店系统
- **商品展示**: 各种道具的展示和购买
- **广告奖励**: 看广告获得道具
- **支付系统**: 预留支付接口

### 6. 通灵系统
- **召唤卷轴**: 基础版和高级版卷轴
- **动物召唤**: 召唤新动物或召回旅行动物
- **远程对话**: 与旅行中的动物对话

## 资源规划

### 1. 美术资源
```
textures/
├── ui/                     # UI相关贴图
│   ├── buttons/           # 按钮贴图
│   ├── panels/            # 面板背景
│   └── icons/             # 图标
├── animals/               # 动物贴图
│   ├── cats/              # 猫类动物
│   ├── dogs/              # 狗类动物
│   └── others/            # 其他动物
├── items/                 # 道具贴图
│   ├── food/              # 食物类
│   ├── toys/              # 玩具类
│   └── tools/             # 工具类
├── scenes/                # 场景背景
│   ├── house/             # 主屋场景
│   ├── backyard/          # 后院场景
│   └── others/            # 其他场景
└── effects/               # 特效贴图
```

### 2. 音频资源
```
audio/
├── bgm/                   # 背景音乐
├── sfx/                   # 音效
└── voice/                 # 语音（预留）
```

## API接口设计（预留）

### 1. 用户相关接口
```
POST /api/user/login       # 用户登录
POST /api/user/register    # 用户注册
GET  /api/user/profile     # 获取用户信息
PUT  /api/user/profile     # 更新用户信息
```

### 2. 动物相关接口
```
GET  /api/animals          # 获取动物列表
POST /api/animals/adopt    # 收养动物
POST /api/animals/travel   # 动物外出旅行
GET  /api/animals/messages # 获取动物消息
```

### 3. 道具相关接口
```
GET  /api/items            # 获取道具列表
POST /api/items/purchase   # 购买道具
POST /api/items/use        # 使用道具
```

### 4. 支付相关接口
```
POST /api/payment/create   # 创建支付订单
POST /api/payment/verify   # 验证支付结果
```

## 数据库表结构设计（预留）

### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    level INT DEFAULT 1,
    experience INT DEFAULT 0,
    coins INT DEFAULT 100,
    gems INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 动物表 (animals)
```sql
CREATE TABLE animals (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL,
    rarity VARCHAR(20) NOT NULL,
    description TEXT,
    icon_path VARCHAR(200),
    personality VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 用户动物关系表 (user_animals)
```sql
CREATE TABLE user_animals (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    animal_id VARCHAR(36) NOT NULL,
    status VARCHAR(20) DEFAULT 'home', -- home, traveling
    adopted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (animal_id) REFERENCES animals(id)
);
```

## 开发计划

### 第一阶段：基础框架 ✅
- [x] 项目架构设计
- [x] 核心管理器开发
- [x] 基础UI系统
- [x] 数据管理系统

### 第二阶段：核心功能
- [ ] 主屋场景开发
- [ ] 后院动物管理
- [ ] 动物来访系统
- [ ] 基础交互功能

### 第三阶段：高级功能
- [ ] 动物旅行系统
- [ ] 虚拟微信功能
- [ ] 道具商店系统
- [ ] 通灵召唤系统

### 第四阶段：优化完善
- [ ] AI对话集成
- [ ] 多平台适配
- [ ] 性能优化
- [ ] 测试完善

## 注意事项

1. **竖屏适配**: 确保所有UI都适配竖屏显示
2. **图层管理**: 所有界面都使用图层形式，避免使用alert
3. **资源预留**: 所有资源路径都预留，便于后期替换
4. **API预留**: 预留后端接口，当前使用Mock数据
5. **平台兼容**: 考虑微信小游戏、iOS、Android的兼容性

## 后续扩展

1. **多语言支持**: 国际化方案
2. **社交功能**: 好友系统、排行榜
3. **活动系统**: 限时活动、节日活动
4. **成就系统**: 丰富的成就体系
5. **数据分析**: 用户行为分析
