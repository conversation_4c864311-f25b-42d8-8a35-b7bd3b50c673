import { _decorator, Component, Node, Button, tween, Vec3, UIOpacity } from 'cc';
import { EventManager, GameEvents } from '../../managers/EventManager.ts';
const { ccclass, property } = _decorator;

/**
 * 抽屉组件
 * 用于主屋场景中的道具存储抽屉
 */
@ccclass('DrawerComponent')
export class DrawerComponent extends Component {
    
    @property({ type: Button, tooltip: "抽屉按钮" })
    public drawerButton: Button = null;
    
    @property({ type: Node, tooltip: "抽屉内容节点" })
    public drawerContent: Node = null;
    
    @property({ type: Node, tooltip: "抽屉背景" })
    public drawerBackground: Node = null;
    
    @property({ tooltip: "抽屉ID" })
    public drawerId: string = "";
    
    @property({ tooltip: "抽屉名称" })
    public drawerName: string = "";
    
    @property({ tooltip: "动画持续时间" })
    public animationDuration: number = 0.3;
    
    @property({ tooltip: "是否默认打开" })
    public defaultOpen: boolean = false;

    private _isOpen: boolean = false;
    private _originalPosition: Vec3 = new Vec3();
    private _openPosition: Vec3 = new Vec3();
    private _uiOpacity: UIOpacity = null;

    onLoad() {
        this.setupComponents();
        this.setupButton();
        this.initializePositions();
        this.setInitialState();
    }

    /**
     * 设置组件
     */
    private setupComponents() {
        if (this.drawerContent) {
            this._uiOpacity = this.drawerContent.getComponent(UIOpacity);
            if (!this._uiOpacity) {
                this._uiOpacity = this.drawerContent.addComponent(UIOpacity);
            }
        }
    }

    /**
     * 设置按钮事件
     */
    private setupButton() {
        if (this.drawerButton) {
            this.drawerButton.node.on(Button.EventType.CLICK, this.onDrawerButtonClick, this);
        }
    }

    /**
     * 初始化位置
     */
    private initializePositions() {
        if (this.drawerContent) {
            this._originalPosition = this.drawerContent.getPosition().clone();
            // 计算打开位置（向下移动）
            this._openPosition = this._originalPosition.clone();
            this._openPosition.y -= 200; // 可以根据需要调整
        }
    }

    /**
     * 设置初始状态
     */
    private setInitialState() {
        this._isOpen = this.defaultOpen;
        if (this.drawerContent) {
            if (this._isOpen) {
                this.drawerContent.setPosition(this._openPosition);
                this._uiOpacity.opacity = 255;
            } else {
                this.drawerContent.setPosition(this._originalPosition);
                this._uiOpacity.opacity = 0;
            }
            this.drawerContent.active = this._isOpen;
        }
    }

    /**
     * 抽屉按钮点击事件
     */
    private onDrawerButtonClick() {
        if (this._isOpen) {
            this.closeDrawer();
        } else {
            this.openDrawer();
        }
    }

    /**
     * 打开抽屉
     */
    public openDrawer() {
        if (this._isOpen || !this.drawerContent) return;
        
        this._isOpen = true;
        this.drawerContent.active = true;
        
        // 发送抽屉打开事件
        EventManager.instance.emit(GameEvents.DRAWER_OPEN, {
            drawerId: this.drawerId,
            drawerName: this.drawerName
        });
        
        // 播放打开动画
        this.playOpenAnimation();
    }

    /**
     * 关闭抽屉
     */
    public closeDrawer() {
        if (!this._isOpen || !this.drawerContent) return;
        
        this._isOpen = false;
        
        // 发送抽屉关闭事件
        EventManager.instance.emit(GameEvents.DRAWER_CLOSE, {
            drawerId: this.drawerId,
            drawerName: this.drawerName
        });
        
        // 播放关闭动画
        this.playCloseAnimation();
    }

    /**
     * 播放打开动画
     */
    private playOpenAnimation() {
        if (!this.drawerContent) return;
        
        // 设置初始状态
        this.drawerContent.setPosition(this._originalPosition);
        this._uiOpacity.opacity = 0;
        
        // 位置动画
        tween(this.drawerContent)
            .to(this.animationDuration, { 
                position: this._openPosition 
            }, { easing: 'backOut' })
            .start();
        
        // 透明度动画
        tween(this._uiOpacity)
            .to(this.animationDuration, { opacity: 255 })
            .start();
    }

    /**
     * 播放关闭动画
     */
    private playCloseAnimation() {
        if (!this.drawerContent) return;
        
        // 位置动画
        tween(this.drawerContent)
            .to(this.animationDuration, { 
                position: this._originalPosition 
            }, { easing: 'backIn' })
            .call(() => {
                this.drawerContent.active = false;
            })
            .start();
        
        // 透明度动画
        tween(this._uiOpacity)
            .to(this.animationDuration, { opacity: 0 })
            .start();
    }

    /**
     * 切换抽屉状态
     */
    public toggleDrawer() {
        if (this._isOpen) {
            this.closeDrawer();
        } else {
            this.openDrawer();
        }
    }

    /**
     * 设置抽屉内容
     */
    public setDrawerContent(content: Node) {
        if (this.drawerContent && content) {
            // 清除现有内容
            this.drawerContent.removeAllChildren();
            
            // 添加新内容
            content.setParent(this.drawerContent);
        }
    }

    /**
     * 添加道具到抽屉
     */
    public addItem(itemNode: Node) {
        if (this.drawerContent && itemNode) {
            itemNode.setParent(this.drawerContent);
        }
    }

    /**
     * 从抽屉移除道具
     */
    public removeItem(itemNode: Node) {
        if (this.drawerContent && itemNode && itemNode.parent === this.drawerContent) {
            itemNode.removeFromParent();
        }
    }

    /**
     * 获取抽屉中的道具数量
     */
    public getItemCount(): number {
        return this.drawerContent ? this.drawerContent.children.length : 0;
    }

    /**
     * 检查抽屉是否为空
     */
    public isEmpty(): boolean {
        return this.getItemCount() === 0;
    }

    /**
     * 检查抽屉是否打开
     */
    public get isOpen(): boolean {
        return this._isOpen;
    }

    /**
     * 获取抽屉ID
     */
    public get id(): string {
        return this.drawerId;
    }

    /**
     * 获取抽屉名称
     */
    public get name(): string {
        return this.drawerName;
    }

    /**
     * 设置抽屉可见性
     */
    public setVisible(visible: boolean) {
        this.node.active = visible;
    }

    /**
     * 设置抽屉可交互性
     */
    public setInteractable(interactable: boolean) {
        if (this.drawerButton) {
            this.drawerButton.interactable = interactable;
        }
    }

    onDestroy() {
        // 清理按钮事件
        if (this.drawerButton) {
            this.drawerButton.node.off(Button.EventType.CLICK, this.onDrawerButtonClick, this);
        }
        
        // 停止所有动画
        if (this.drawerContent) {
            this.drawerContent.stopAllActions();
        }
    }
}
