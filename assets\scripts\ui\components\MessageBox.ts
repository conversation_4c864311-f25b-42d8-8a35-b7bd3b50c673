import { _decorator, Component, Node, Label, Button } from 'cc';
import { BasePanel } from '../BasePanel.ts';
const { ccclass, property } = _decorator;

/**
 * 消息框组件
 * 用于显示各种提示消息、确认对话框等
 */
@ccclass('MessageBox')
export class MessageBox extends BasePanel {
    
    @property({ type: Label, tooltip: "标题标签" })
    public titleLabel: Label = null;
    
    @property({ type: Label, tooltip: "内容标签" })
    public contentLabel: Label = null;
    
    @property({ type: Button, tooltip: "确认按钮" })
    public confirmButton: Button = null;
    
    @property({ type: Button, tooltip: "取消按钮" })
    public cancelButton: Button = null;
    
    @property({ type: Node, tooltip: "按钮容器" })
    public buttonContainer: Node = null;

    private _onConfirm: Function = null;
    private _onCancel: Function = null;
    private _messageType: MessageType = MessageType.INFO;

    onLoad() {
        super.onLoad();
        this.setupButtons();
    }

    /**
     * 设置按钮事件
     */
    private setupButtons() {
        if (this.confirmButton) {
            this.confirmButton.node.on(Button.EventType.CLICK, this.onConfirmClick, this);
        }
        
        if (this.cancelButton) {
            this.cancelButton.node.on(Button.EventType.CLICK, this.onCancelClick, this);
        }
    }

    /**
     * 初始化消息框
     */
    protected onInit(data?: MessageBoxData) {
        if (!data) return;
        
        this._messageType = data.type || MessageType.INFO;
        this._onConfirm = data.onConfirm;
        this._onCancel = data.onCancel;
        
        // 设置标题
        if (this.titleLabel) {
            this.titleLabel.string = data.title || this.getDefaultTitle();
        }
        
        // 设置内容
        if (this.contentLabel) {
            this.contentLabel.string = data.content || "";
        }
        
        // 设置按钮显示
        this.setupButtonDisplay(data);
    }

    /**
     * 获取默认标题
     */
    private getDefaultTitle(): string {
        switch (this._messageType) {
            case MessageType.INFO:
                return "提示";
            case MessageType.WARNING:
                return "警告";
            case MessageType.ERROR:
                return "错误";
            case MessageType.CONFIRM:
                return "确认";
            case MessageType.SUCCESS:
                return "成功";
            default:
                return "消息";
        }
    }

    /**
     * 设置按钮显示
     */
    private setupButtonDisplay(data: MessageBoxData) {
        const showCancel = this._messageType === MessageType.CONFIRM || data.showCancel;
        
        if (this.cancelButton) {
            this.cancelButton.node.active = showCancel;
        }
        
        if (this.confirmButton) {
            this.confirmButton.node.active = true;
            // 设置确认按钮文本
            const confirmLabel = this.confirmButton.getComponentInChildren(Label);
            if (confirmLabel) {
                confirmLabel.string = data.confirmText || "确定";
            }
        }
        
        if (this.cancelButton && showCancel) {
            // 设置取消按钮文本
            const cancelLabel = this.cancelButton.getComponentInChildren(Label);
            if (cancelLabel) {
                cancelLabel.string = data.cancelText || "取消";
            }
        }
    }

    /**
     * 确认按钮点击事件
     */
    private onConfirmClick() {
        if (this._onConfirm) {
            this._onConfirm();
        }
        this.close();
    }

    /**
     * 取消按钮点击事件
     */
    private onCancelClick() {
        if (this._onCancel) {
            this._onCancel();
        }
        this.close();
    }

    /**
     * 静态方法：显示信息提示
     */
    public static showInfo(content: string, title?: string, onConfirm?: Function) {
        // 这里应该通过UIManager来显示
        console.log(`[信息] ${title || "提示"}: ${content}`);
        if (onConfirm) onConfirm();
    }

    /**
     * 静态方法：显示警告
     */
    public static showWarning(content: string, title?: string, onConfirm?: Function) {
        console.log(`[警告] ${title || "警告"}: ${content}`);
        if (onConfirm) onConfirm();
    }

    /**
     * 静态方法：显示错误
     */
    public static showError(content: string, title?: string, onConfirm?: Function) {
        console.log(`[错误] ${title || "错误"}: ${content}`);
        if (onConfirm) onConfirm();
    }

    /**
     * 静态方法：显示确认对话框
     */
    public static showConfirm(content: string, onConfirm?: Function, onCancel?: Function, title?: string) {
        console.log(`[确认] ${title || "确认"}: ${content}`);
        // 这里应该通过UIManager来显示确认对话框
        if (onConfirm) onConfirm();
    }

    /**
     * 静态方法：显示成功提示
     */
    public static showSuccess(content: string, title?: string, onConfirm?: Function) {
        console.log(`[成功] ${title || "成功"}: ${content}`);
        if (onConfirm) onConfirm();
    }

    onDestroy() {
        super.onDestroy();
        
        // 清理按钮事件
        if (this.confirmButton) {
            this.confirmButton.node.off(Button.EventType.CLICK, this.onConfirmClick, this);
        }
        
        if (this.cancelButton) {
            this.cancelButton.node.off(Button.EventType.CLICK, this.onCancelClick, this);
        }
    }
}

/**
 * 消息类型枚举
 */
export enum MessageType {
    INFO = "info",
    WARNING = "warning", 
    ERROR = "error",
    CONFIRM = "confirm",
    SUCCESS = "success"
}

/**
 * 消息框数据接口
 */
export interface MessageBoxData {
    type?: MessageType;
    title?: string;
    content: string;
    confirmText?: string;
    cancelText?: string;
    showCancel?: boolean;
    onConfirm?: Function;
    onCancel?: Function;
}
