import { _decorator, Component, Node, Button } from 'cc';
import { BaseScene } from './BaseScene';
import { DrawerComponent } from '../ui/components/DrawerComponent';
import { ItemDisplay } from '../ui/components/ItemDisplay';
import { ItemData, ItemType } from '../data/ItemData';
import { GameEvents } from '../managers/EventManager';
const { ccclass, property } = _decorator;

/**
 * 主屋场景控制器
 * 管理房屋内的抽屉系统和道具存储
 */
@ccclass('HouseScene')
export class HouseScene extends BaseScene {
    
    @property({ type: [DrawerComponent], tooltip: "抽屉组件列表" })
    public drawers: DrawerComponent[] = [];
    
    @property({ type: Node, tooltip: "道具展示区域" })
    public itemDisplayArea: Node = null;
    
    @property({ type: Button, tooltip: "整理按钮" })
    public organizeButton: Button = null;
    
    @property({ type: Button, tooltip: "清理按钮" })
    public cleanButton: Button = null;
    
    @property({ type: Node, tooltip: "房间装饰容器" })
    public decorationContainer: Node = null;

    private _currentOpenDrawer: DrawerComponent = null;
    private _itemDisplays: Map<string, ItemDisplay> = new Map();

    onLoad() {
        super.onLoad();
        this.sceneName = "HouseScene";
        this.setupButtons();
        this.setupDrawers();
    }

    protected onSceneStart() {
        super.onSceneStart();
        this.initializeHouse();
        this.loadPlayerItems();
        this.registerHouseEvents();
    }

    /**
     * 设置按钮事件
     */
    private setupButtons() {
        if (this.organizeButton) {
            this.organizeButton.node.on(Button.EventType.CLICK, this.onOrganizeClick, this);
        }
        
        if (this.cleanButton) {
            this.cleanButton.node.on(Button.EventType.CLICK, this.onCleanClick, this);
        }
    }

    /**
     * 设置抽屉
     */
    private setupDrawers() {
        this.drawers.forEach((drawer, index) => {
            if (!drawer.id) {
                drawer.drawerId = `drawer_${index}`;
            }
            if (!drawer.name) {
                drawer.drawerName = `抽屉${index + 1}`;
            }
        });
    }

    /**
     * 初始化房屋
     */
    private initializeHouse() {
        console.log("初始化主屋场景");
        
        // 设置房屋装饰
        this.setupDecorations();
        
        // 初始化抽屉状态
        this.initializeDrawerStates();
    }

    /**
     * 设置房屋装饰
     */
    private setupDecorations() {
        const playerData = this.getPlayerData();
        const decorations = playerData.inventory.filter(item => {
            const itemData = this.getItemData().find(data => data.id === item.itemId);
            return itemData?.type === ItemType.DECORATION;
        });
        
        // 应用装饰
        decorations.forEach(decoration => {
            this.applyDecoration(decoration.itemId);
        });
    }

    /**
     * 应用装饰
     */
    private applyDecoration(decorationId: string) {
        const itemData = this.getItemData().find(data => data.id === decorationId);
        if (itemData && this.decorationContainer) {
            console.log(`应用装饰: ${itemData.name}`);
            // 这里应该实例化装饰预制体并添加到容器中
        }
    }

    /**
     * 初始化抽屉状态
     */
    private initializeDrawerStates() {
        // 根据玩家数据恢复抽屉状态
        const playerData = this.getPlayerData();
        
        this.drawers.forEach(drawer => {
            // 这里可以根据保存的数据恢复抽屉的开关状态
            drawer.closeDrawer(); // 默认关闭所有抽屉
        });
    }

    /**
     * 加载玩家道具
     */
    private loadPlayerItems() {
        const playerData = this.getPlayerData();
        const itemData = this.getItemData();
        
        // 按类型分类道具
        const itemsByType = this.categorizeItems(playerData.inventory, itemData);
        
        // 将道具分配到对应的抽屉
        this.distributeItemsToDrawers(itemsByType);
    }

    /**
     * 按类型分类道具
     */
    private categorizeItems(inventory: any[], itemData: ItemData[]) {
        const categories = {
            [ItemType.FOOD]: [],
            [ItemType.TOY]: [],
            [ItemType.TOOL]: [],
            [ItemType.SUMMON_SCROLL]: [],
            [ItemType.DECORATION]: []
        };
        
        inventory.forEach(invItem => {
            const item = itemData.find(data => data.id === invItem.itemId);
            if (item && categories[item.type]) {
                categories[item.type].push({
                    itemData: item,
                    count: invItem.count
                });
            }
        });
        
        return categories;
    }

    /**
     * 将道具分配到抽屉
     */
    private distributeItemsToDrawers(itemsByType: any) {
        // 为每种类型的道具分配抽屉
        const typeToDrawerMap = {
            [ItemType.FOOD]: 0,
            [ItemType.TOY]: 1,
            [ItemType.TOOL]: 2,
            [ItemType.SUMMON_SCROLL]: 3,
            [ItemType.DECORATION]: 4
        };
        
        Object.keys(itemsByType).forEach(type => {
            const drawerIndex = typeToDrawerMap[type];
            if (drawerIndex < this.drawers.length) {
                const drawer = this.drawers[drawerIndex];
                const items = itemsByType[type];
                this.populateDrawer(drawer, items);
            }
        });
    }

    /**
     * 填充抽屉内容
     */
    private populateDrawer(drawer: DrawerComponent, items: any[]) {
        if (!drawer.drawerContent) return;
        
        // 清空抽屉
        drawer.drawerContent.removeAllChildren();
        
        // 添加道具显示
        items.forEach(item => {
            const itemDisplay = this.createItemDisplay(item.itemData, item.count);
            if (itemDisplay) {
                drawer.addItem(itemDisplay.node);
            }
        });
    }

    /**
     * 创建道具显示
     */
    private createItemDisplay(itemData: ItemData, count: number): ItemDisplay {
        // 这里应该实例化ItemDisplay预制体
        // 暂时返回null，实际实现中需要加载预制体
        console.log(`创建道具显示: ${itemData.name} x${count}`);
        return null;
    }

    /**
     * 注册房屋事件
     */
    private registerHouseEvents() {
        // 监听抽屉事件
        this.node.on(GameEvents.DRAWER_OPEN, this.onDrawerOpen, this);
        this.node.on(GameEvents.DRAWER_CLOSE, this.onDrawerClose, this);
        
        // 监听道具事件
        this.node.on(GameEvents.ITEM_USE, this.onItemUse, this);
        this.node.on(GameEvents.ITEM_OBTAINED, this.onItemObtained, this);
    }

    /**
     * 抽屉打开事件
     */
    private onDrawerOpen(event: any) {
        const { drawerId } = event.detail || event;
        
        // 关闭其他抽屉
        this.drawers.forEach(drawer => {
            if (drawer.id !== drawerId && drawer.isOpen) {
                drawer.closeDrawer();
            }
        });
        
        // 记录当前打开的抽屉
        this._currentOpenDrawer = this.drawers.find(d => d.id === drawerId);
        
        console.log(`抽屉打开: ${drawerId}`);
    }

    /**
     * 抽屉关闭事件
     */
    private onDrawerClose(event: any) {
        const { drawerId } = event.detail || event;
        
        if (this._currentOpenDrawer?.id === drawerId) {
            this._currentOpenDrawer = null;
        }
        
        console.log(`抽屉关闭: ${drawerId}`);
    }

    /**
     * 道具使用事件
     */
    private onItemUse(event: any) {
        const { itemId, itemData } = event.detail || event;
        console.log(`使用道具: ${itemData?.name || itemId}`);
        
        // 根据道具类型执行不同的使用逻辑
        this.handleItemUse(itemData);
    }

    /**
     * 处理道具使用
     */
    private handleItemUse(itemData: ItemData) {
        if (!itemData) return;
        
        switch (itemData.type) {
            case ItemType.SUMMON_SCROLL:
                this.useSummonScroll(itemData);
                break;
            case ItemType.DECORATION:
                this.useDecoration(itemData);
                break;
            default:
                this.showMessage(`使用了 ${itemData.name}`);
                break;
        }
    }

    /**
     * 使用召唤卷轴
     */
    private useSummonScroll(itemData: ItemData) {
        this.showConfirm(
            "使用召唤卷轴",
            `确定要使用 ${itemData.name} 吗？这将召唤一只新的动物朋友！`,
            () => {
                // 执行召唤逻辑
                console.log(`使用召唤卷轴: ${itemData.name}`);
                this.showSuccess("召唤成功！新的朋友正在路上！");
            }
        );
    }

    /**
     * 使用装饰
     */
    private useDecoration(itemData: ItemData) {
        this.showMessage(`放置了 ${itemData.name}`);
        this.applyDecoration(itemData.id);
    }

    /**
     * 道具获得事件
     */
    private onItemObtained(event: any) {
        const { itemId, count } = event.detail || event;
        console.log(`获得道具: ${itemId} x${count}`);
        
        // 重新加载道具显示
        this.loadPlayerItems();
    }

    /**
     * 整理按钮点击事件
     */
    private onOrganizeClick() {
        this.showMessage("正在整理房间...");
        
        // 重新分类和分配道具
        this.loadPlayerItems();
        
        // 关闭所有抽屉
        this.drawers.forEach(drawer => {
            if (drawer.isOpen) {
                drawer.closeDrawer();
            }
        });
        
        this.showSuccess("房间整理完成！");
    }

    /**
     * 清理按钮点击事件
     */
    private onCleanClick() {
        this.showMessage("正在清理房间...");
        
        // 播放清理动画
        this.scheduleOnce(() => {
            this.showSuccess("房间清理完成！房间变得更加整洁了！");
        }, 1);
    }

    protected onSceneDestroy() {
        super.onSceneDestroy();
        
        // 清理事件监听
        this.node.off(GameEvents.DRAWER_OPEN, this.onDrawerOpen, this);
        this.node.off(GameEvents.DRAWER_CLOSE, this.onDrawerClose, this);
        this.node.off(GameEvents.ITEM_USE, this.onItemUse, this);
        this.node.off(GameEvents.ITEM_OBTAINED, this.onItemObtained, this);
        
        // 清理按钮事件
        if (this.organizeButton) {
            this.organizeButton.node.off(Button.EventType.CLICK, this.onOrganizeClick, this);
        }
        
        if (this.cleanButton) {
            this.cleanButton.node.off(Button.EventType.CLICK, this.onCleanClick, this);
        }
    }
}
