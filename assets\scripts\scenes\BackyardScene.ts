import { _decorator, Component, Node, ScrollView, Prefab, instantiate } from 'cc';
import { BaseScene } from './BaseScene';
import { AnimalDisplay } from '../ui/components/AnimalDisplay';
import { AnimalInstance, AnimalStatus } from '../data/AnimalInstance';
import { GameEvents, AnimalEventData } from '../managers/EventManager';
const { ccclass, property } = _decorator;

/**
 * 后院场景控制器
 * 管理动物的显示和交互
 */
@ccclass('BackyardScene')
export class BackyardScene extends BaseScene {
    
    @property({ type: ScrollView, tooltip: "动物列表滚动视图" })
    public animalScrollView: ScrollView = null;
    
    @property({ type: Node, tooltip: "动物容器" })
    public animalContainer: Node = null;
    
    @property({ type: Prefab, tooltip: "动物显示预制体" })
    public animalDisplayPrefab: Prefab = null;
    
    @property({ type: Node, tooltip: "空状态提示" })
    public emptyStateNode: Node = null;
    
    @property({ type: Node, tooltip: "动物活动区域" })
    public activityArea: Node = null;

    private _animalDisplays: Map<string, AnimalDisplay> = new Map();
    private _currentAnimals: AnimalInstance[] = [];

    onLoad() {
        super.onLoad();
        this.sceneName = "BackyardScene";
    }

    protected onSceneStart() {
        super.onSceneStart();
        this.initializeBackyard();
        this.loadAnimals();
        this.registerBackyardEvents();
        this.startAnimalUpdates();
    }

    /**
     * 初始化后院
     */
    private initializeBackyard() {
        console.log("初始化后院场景");
        
        // 设置滚动视图
        if (this.animalScrollView && !this.animalContainer) {
            this.animalContainer = this.animalScrollView.content;
        }
        
        // 初始化空状态
        this.updateEmptyState();
    }

    /**
     * 加载动物
     */
    private loadAnimals() {
        const playerData = this.getPlayerData();
        
        // 获取玩家拥有的动物实例
        this._currentAnimals = this.getPlayerAnimalInstances();
        
        // 过滤出在家的动物
        const homeAnimals = this._currentAnimals.filter(animal => 
            animal.status === AnimalStatus.HOME || 
            animal.status === AnimalStatus.PLAYING ||
            animal.status === AnimalStatus.EATING ||
            animal.status === AnimalStatus.SLEEPING
        );
        
        // 创建动物显示
        this.createAnimalDisplays(homeAnimals);
        
        // 更新空状态
        this.updateEmptyState();
    }

    /**
     * 获取玩家动物实例
     */
    private getPlayerAnimalInstances(): AnimalInstance[] {
        // 这里应该从玩家数据中获取动物实例
        // 暂时返回空数组，实际实现中需要在PlayerData中添加animalInstances
        const playerData = this.getPlayerData();
        
        // Mock一些动物实例用于测试
        const mockAnimals: AnimalInstance[] = [
            {
                instanceId: "animal_001",
                animalId: "cat_001",
                customName: "小花",
                level: 3,
                experience: 450,
                happiness: 85,
                health: 90,
                cleanliness: 75,
                hunger: 60,
                friendship: 70,
                adoptedTime: Date.now() - 86400000 * 7, // 7天前收养
                lastInteractTime: Date.now() - 3600000, // 1小时前互动
                totalInteractions: 25,
                status: AnimalStatus.HOME,
                traits: ["playful", "curious"],
                memories: []
            },
            {
                instanceId: "animal_002",
                animalId: "dog_001",
                customName: "旺财",
                level: 5,
                experience: 1200,
                happiness: 95,
                health: 85,
                cleanliness: 80,
                hunger: 40,
                friendship: 90,
                adoptedTime: Date.now() - 86400000 * 14, // 14天前收养
                lastInteractTime: Date.now() - 1800000, // 30分钟前互动
                totalInteractions: 50,
                status: AnimalStatus.PLAYING,
                traits: ["loyal", "brave"],
                memories: []
            }
        ];
        
        return mockAnimals;
    }

    /**
     * 创建动物显示
     */
    private createAnimalDisplays(animals: AnimalInstance[]) {
        if (!this.animalContainer) return;
        
        // 清除现有显示
        this.clearAnimalDisplays();
        
        // 为每个动物创建显示组件
        animals.forEach(animal => {
            const displayNode = this.createAnimalDisplayNode(animal);
            if (displayNode) {
                this.animalContainer.addChild(displayNode);
                
                const display = displayNode.getComponent(AnimalDisplay);
                if (display) {
                    this._animalDisplays.set(animal.instanceId, display);
                }
            }
        });
    }

    /**
     * 创建动物显示节点
     */
    private createAnimalDisplayNode(animal: AnimalInstance): Node {
        if (!this.animalDisplayPrefab) {
            console.warn("动物显示预制体未设置");
            return null;
        }
        
        const displayNode = instantiate(this.animalDisplayPrefab);
        const display = displayNode.getComponent(AnimalDisplay);
        
        if (display) {
            display.setAnimalData(animal);
        }
        
        return displayNode;
    }

    /**
     * 清除动物显示
     */
    private clearAnimalDisplays() {
        if (this.animalContainer) {
            this.animalContainer.removeAllChildren();
        }
        this._animalDisplays.clear();
    }

    /**
     * 更新空状态显示
     */
    private updateEmptyState() {
        const hasAnimals = this._currentAnimals.length > 0;
        
        if (this.emptyStateNode) {
            this.emptyStateNode.active = !hasAnimals;
        }
        
        if (this.animalScrollView) {
            this.animalScrollView.node.active = hasAnimals;
        }
    }

    /**
     * 注册后院事件
     */
    private registerBackyardEvents() {
        // 监听动物相关事件
        this.node.on(GameEvents.ANIMAL_INTERACT, this.onAnimalInteract, this);
        this.node.on(GameEvents.ANIMAL_LEVEL_UP, this.onAnimalLevelUp, this);
        this.node.on(GameEvents.ANIMAL_STATUS_CHANGE, this.onAnimalStatusChange, this);
        this.node.on(GameEvents.ANIMAL_ADOPTED, this.onAnimalAdopted, this);
        this.node.on(GameEvents.ANIMAL_TRAVEL_RETURN, this.onAnimalTravelReturn, this);
    }

    /**
     * 开始动物状态更新
     */
    private startAnimalUpdates() {
        // 每30秒更新一次动物状态
        this.schedule(this.updateAnimalStates, 30);
    }

    /**
     * 更新动物状态
     */
    private updateAnimalStates() {
        this._currentAnimals.forEach(animal => {
            this.updateAnimalState(animal);
        });
        
        // 刷新显示
        this.refreshAnimalDisplays();
    }

    /**
     * 更新单个动物状态
     */
    private updateAnimalState(animal: AnimalInstance) {
        const now = Date.now();
        const timeSinceLastInteract = now - animal.lastInteractTime;
        
        // 随时间降低属性值
        if (timeSinceLastInteract > 3600000) { // 1小时
            animal.hunger = Math.max(0, animal.hunger - 5);
            animal.happiness = Math.max(0, animal.happiness - 3);
            animal.cleanliness = Math.max(0, animal.cleanliness - 2);
        }
        
        // 根据属性值更新状态
        if (animal.hunger < 20) {
            animal.status = AnimalStatus.EATING;
        } else if (animal.happiness > 80 && Math.random() < 0.3) {
            animal.status = AnimalStatus.PLAYING;
        } else if (animal.health < 50) {
            animal.status = AnimalStatus.SICK;
        } else if (Math.random() < 0.1) {
            animal.status = AnimalStatus.SLEEPING;
        } else {
            animal.status = AnimalStatus.HOME;
        }
    }

    /**
     * 刷新动物显示
     */
    private refreshAnimalDisplays() {
        this._animalDisplays.forEach((display, instanceId) => {
            const animal = this._currentAnimals.find(a => a.instanceId === instanceId);
            if (animal) {
                display.setAnimalData(animal);
            }
        });
    }

    /**
     * 动物互动事件
     */
    private onAnimalInteract(event: any) {
        const { instanceId } = event.detail || event;
        console.log(`动物互动: ${instanceId}`);
        
        // 刷新对应的动物显示
        const display = this._animalDisplays.get(instanceId);
        if (display) {
            const animal = this._currentAnimals.find(a => a.instanceId === instanceId);
            if (animal) {
                display.setAnimalData(animal);
            }
        }
    }

    /**
     * 动物升级事件
     */
    private onAnimalLevelUp(event: any) {
        const { instanceId } = event.detail || event;
        this.showSuccess("恭喜！动物升级了！");
        
        // 播放升级特效
        this.playLevelUpEffect(instanceId);
    }

    /**
     * 播放升级特效
     */
    private playLevelUpEffect(instanceId: string) {
        const display = this._animalDisplays.get(instanceId);
        if (display) {
            // 这里可以播放升级特效
            console.log(`播放升级特效: ${instanceId}`);
        }
    }

    /**
     * 动物状态变化事件
     */
    private onAnimalStatusChange(event: any) {
        const { instanceId, newStatus } = event.detail || event;
        console.log(`动物状态变化: ${instanceId} -> ${newStatus}`);
        
        // 刷新显示
        this.refreshAnimalDisplays();
    }

    /**
     * 动物收养事件
     */
    private onAnimalAdopted(event: any) {
        const { animalId } = event.detail || event;
        this.showSuccess("欢迎新朋友加入！");
        
        // 重新加载动物列表
        this.loadAnimals();
    }

    /**
     * 动物旅行归来事件
     */
    private onAnimalTravelReturn(event: any) {
        const { instanceId } = event.detail || event;
        this.showSuccess("动物朋友旅行归来了！");
        
        // 重新加载动物列表
        this.loadAnimals();
    }

    /**
     * 添加新动物
     */
    public addAnimal(animalInstance: AnimalInstance) {
        this._currentAnimals.push(animalInstance);
        
        // 创建显示
        const displayNode = this.createAnimalDisplayNode(animalInstance);
        if (displayNode && this.animalContainer) {
            this.animalContainer.addChild(displayNode);
            
            const display = displayNode.getComponent(AnimalDisplay);
            if (display) {
                this._animalDisplays.set(animalInstance.instanceId, display);
            }
        }
        
        // 更新空状态
        this.updateEmptyState();
    }

    /**
     * 移除动物
     */
    public removeAnimal(instanceId: string) {
        // 从列表中移除
        const index = this._currentAnimals.findIndex(a => a.instanceId === instanceId);
        if (index !== -1) {
            this._currentAnimals.splice(index, 1);
        }
        
        // 移除显示
        const display = this._animalDisplays.get(instanceId);
        if (display) {
            display.node.removeFromParent();
            this._animalDisplays.delete(instanceId);
        }
        
        // 更新空状态
        this.updateEmptyState();
    }

    protected onSceneDestroy() {
        super.onSceneDestroy();
        
        // 停止定时器
        this.unscheduleAllCallbacks();
        
        // 清理事件监听
        this.node.off(GameEvents.ANIMAL_INTERACT, this.onAnimalInteract, this);
        this.node.off(GameEvents.ANIMAL_LEVEL_UP, this.onAnimalLevelUp, this);
        this.node.off(GameEvents.ANIMAL_STATUS_CHANGE, this.onAnimalStatusChange, this);
        this.node.off(GameEvents.ANIMAL_ADOPTED, this.onAnimalAdopted, this);
        this.node.off(GameEvents.ANIMAL_TRAVEL_RETURN, this.onAnimalTravelReturn, this);
        
        // 清理显示
        this.clearAnimalDisplays();
    }
}
