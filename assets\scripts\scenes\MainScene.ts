import { _decorator, Component, Node, Button, Label } from 'cc';
import { BaseScene } from './BaseScene.ts';
import { SceneNames } from '../managers/SceneManager.ts';
import { GameConfig } from '../config/GameConfig.ts';
const { ccclass, property } = _decorator;

/**
 * 主场景控制器
 * 游戏的主入口场景，包含主要的导航功能
 */
@ccclass('MainScene')
export class MainScene extends BaseScene {
    
    @property({ type: Button, tooltip: "进入主屋按钮" })
    public houseButton: Button = null;
    
    @property({ type: Button, tooltip: "进入后院按钮" })
    public backyardButton: Button = null;
    
    @property({ type: Button, tooltip: "打开商店按钮" })
    public shopButton: Button = null;
    
    @property({ type: Button, tooltip: "打开微信按钮" })
    public wechatButton: Button = null;
    
    @property({ type: Label, tooltip: "玩家名称标签" })
    public playerNameLabel: Label = null;
    
    @property({ type: Label, tooltip: "金币数量标签" })
    public coinsLabel: Label = null;
    
    @property({ type: Label, tooltip: "宝石数量标签" })
    public gemsLabel: Label = null;
    
    @property({ type: Node, tooltip: "动物来访提示" })
    public animalVisitNotification: Node = null;

    onLoad() {
        super.onLoad();
        this.sceneName = "MainScene";
        this.showBackButton = false; // 主场景不显示返回按钮
        this.setupButtons();
    }

    protected onSceneStart() {
        super.onSceneStart();
        this.updateUI();
        this.checkAnimalVisit();
        this.playBackgroundMusic();
        this.checkDailyReward();
    }

    /**
     * 设置按钮事件
     */
    private setupButtons() {
        if (this.houseButton) {
            this.houseButton.node.on(Button.EventType.CLICK, this.onHouseButtonClick, this);
        }
        
        if (this.backyardButton) {
            this.backyardButton.node.on(Button.EventType.CLICK, this.onBackyardButtonClick, this);
        }
        
        if (this.shopButton) {
            this.shopButton.node.on(Button.EventType.CLICK, this.onShopButtonClick, this);
        }
        
        if (this.wechatButton) {
            this.wechatButton.node.on(Button.EventType.CLICK, this.onWeChatButtonClick, this);
        }
    }

    /**
     * 更新UI显示
     */
    private updateUI() {
        const playerData = this.getPlayerData();

        if (this.playerNameLabel) {
            this.playerNameLabel.string = playerData.playerName;
        }

        if (this.coinsLabel) {
            this.coinsLabel.string = playerData.coins.toString();
        }

        if (this.gemsLabel) {
            this.gemsLabel.string = playerData.gems.toString();
        }
    }

    /**
     * 检查动物来访
     */
    private checkAnimalVisit() {
        // 这里实现动物来访的逻辑
        // 根据时间和概率决定是否有动物来访
        const shouldShowVisit = this.shouldAnimalVisit();
        
        if (this.animalVisitNotification) {
            this.animalVisitNotification.active = shouldShowVisit;
        }
    }

    /**
     * 判断是否应该有动物来访
     */
    private shouldAnimalVisit(): boolean {
        // 简单的随机逻辑，后续可以根据时间、道具等因素优化
        return Math.random() < 0.3; // 30%的概率
    }

    /**
     * 播放背景音乐
     */
    private playBackgroundMusic() {
        // 这里实现背景音乐播放逻辑
        console.log("播放主场景背景音乐");
    }

    /**
     * 检查每日登录奖励
     */
    private checkDailyReward() {
        const playerData = this.getPlayerData();
        const now = Date.now();
        const lastLogin = playerData.lastLoginTime;

        // 检查是否是新的一天
        const isNewDay = !this.isSameDay(new Date(lastLogin), new Date(now));

        if (isNewDay) {
            this.showDailyReward();
            playerData.lastLoginTime = now;
            this.saveSceneData();
        }
    }

    /**
     * 判断是否是同一天
     */
    private isSameDay(date1: Date, date2: Date): boolean {
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }

    /**
     * 显示每日登录奖励
     */
    private showDailyReward() {
        this.showSuccess("获得每日登录奖励！");

        // 添加奖励
        this.addCoins(GameConfig.DAILY_REWARD.COINS);
        this.addGems(GameConfig.DAILY_REWARD.GEMS);

        // 更新UI
        this.updateUI();
    }

    /**
     * 主屋按钮点击事件
     */
    private onHouseButtonClick() {
        console.log("进入主屋");
        this.gotoScene(SceneNames.HOUSE);
    }

    /**
     * 后院按钮点击事件
     */
    private onBackyardButtonClick() {
        console.log("进入后院");
        this.gotoScene(SceneNames.BACKYARD);
    }

    /**
     * 商店按钮点击事件
     */
    private onShopButtonClick() {
        console.log("打开商店");
        this.gotoScene(SceneNames.SHOP);
    }

    /**
     * 微信按钮点击事件
     */
    private onWeChatButtonClick() {
        console.log("打开微信");
        this.gotoScene(SceneNames.WECHAT);
    }

    /**
     * 动物来访通知点击事件
     */
    public onAnimalVisitClick() {
        console.log("有动物来访！");
        // 这里可以显示动物来访的详细界面
        UIManager.instance.showMessage("有新的动物朋友来访问！");
    }

    onDestroy() {
        // 清理事件监听
        if (this.houseButton) {
            this.houseButton.node.off(Button.EventType.CLICK, this.onHouseButtonClick, this);
        }
        if (this.backyardButton) {
            this.backyardButton.node.off(Button.EventType.CLICK, this.onBackyardButtonClick, this);
        }
        if (this.shopButton) {
            this.shopButton.node.off(Button.EventType.CLICK, this.onShopButtonClick, this);
        }
        if (this.wechatButton) {
            this.wechatButton.node.off(Button.EventType.CLICK, this.onWeChatButtonClick, this);
        }
    }
}
