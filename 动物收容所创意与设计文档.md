# 动物收容所创意与设计文档

> 本文档用于记录和整理游戏开发过程中的灵感、玩法设定、变现思路、道具设计等内容，便于持续更新和后续实现。

---

## 一、游戏核心创意
- 灵感来源：旅行青蛙、猫咪后院等治愈系收集养成游戏。
- 主题设定：玩家作为动物收容所管理员，收留、照顾、互动各类来访动物。
- 主要玩法：
  - 动物随机来访，玩家可选择收留。
  - 玩家可外用手机去现实环境拍照，识别新动物并有几率（捕获）收留。
  - 动物在收容所内玩耍、休息、互动（AI对话）。
  - 动物会外出旅行，并通过"微信"模拟界面向玩家发送照片、语音、文字等消息，分享奇闻轶事。

---

## 二、变现思路
- 主要收入方式：
  1. **看广告获得激励道具**
  2. **购买互动道具**

### 1. 看广告获得的激励道具
- **有概率的美味食物/零食/玩具**：提升动物来访概率，吸引稀有动物。
- **加速券**：减少动物来访等待时间。

### 2. 可购买的互动道具

- **互动道具**：如语音翻译器、拍立得相机，解锁AI对话、合影等。 
- **美食礼包**：提升好感度。

---

## 三、激励与付费道具结合建议
- 广告获得为基础版，付费为高级版。
- 部分道具可通过签到/广告获得碎片兑换。
- 道具可叠加使用，提升效果。

---

## 四、示例激励文案
- "看广告获得【猫薄荷球】，吸引更多小猫来玩耍！"
- "用【幸运铃铛】，下次来访的动物必定是新朋友！"


---

## 五、后续可补充内容
- 具体动物设定与性格档案
- AI对话话题与安全策略
- 微信消息模拟界面设计
- 玩法节奏与内容更新计划
- UI/美术风格参考

---

## 六、通灵之术（召唤卷轴）设计

### 1. 功能说明
通灵之术为消耗品道具（卷轴），使用后可选择以下功能：
- **召唤一个随机新动物来访**
  - 有概率召唤稀有动物。
- **召回一个正在外出旅行的动物**
  - 设定为动物在外旅行时遇到危险（AI生成情节），玩家可用通灵之术提前召回，动物有可能带回礼物或特殊故事。
- **与正在外出旅行的动物远程对话**
  - 通过虚拟微信联系（需拥有虚拟手机/虚拟微信道具），获得动物旅行中的见闻、照片、语音等。

### 2. 获得方式
- **基础版通灵之术（卷轴）**
  - 通过看广告获得，每日限次，鼓励活跃和广告变现。
- **高级版通灵之术（卷轴）**
  - 商城购买，无次数限制。
  - 高级版可提升召唤稀有动物概率，或带来特殊效果。

### 3. 平衡性建议
- 通灵之术为消耗品，使用后消失。
- 每日广告获得次数有限，防止滥用。
- 高级版可作为付费道具，提升付费吸引力。
- 远程对话功能需玩家拥有虚拟手机/虚拟微信等道具，增加道具联动和付费点。

### 4. 激励文案示例
- "施展通灵之术，召唤神秘动物来访！"
- "用通灵之术，把你思念的小动物召回身边！"
- "通灵之术发动，和远方旅行的动物连线聊天！"

---

## 七、主人公人设与召唤卷轴获得情节

### 1. 主人公人设
- **姓名**：可自定义（如林悠然）
- **年龄**：20-30岁（模糊设定，便于玩家代入）
- **性格**：温和善良、富有同理心、喜欢小动物、乐于助人，有点佛系但内心坚韧。
- **背景故事**：
  - 厌倦大城市996的快节奏和压力，决定回到家乡或来到一座安静的小镇。
  - 租下一间老屋，改造成动物收容所，梦想为流浪、受伤、迷路的动物提供一个温暖的家。
  - 希望通过自己的努力让更多人关注动物保护，也让自己找回生活的意义和温度。

### 2. 获得召唤卷轴的情节
- 一天夜里，主人公在收容所整理旧物时，发现了一个神秘的古老箱子。箱子上有奇异的动物图案和一行模糊的字迹："善待生命者，得通灵之力。"
- 打开箱子后，发现一卷泛着微光的卷轴。此时窗外传来一阵风，卷轴自动展开，浮现出一只可爱的灵兽（如会说话的小狐狸或猫头鹰）。
- 灵兽告诉主人公：
  > "你对动物的善意和守护之心，感动了守护动物世界的精灵。我们赐予你'通灵之术'，让你能与更多动物结缘，也能在它们遇到危险时伸出援手。请善用这份力量，守护每一个需要帮助的小生命。"
- 从此，主人公获得了召唤卷轴，可以召唤、守护、沟通各种动物，开启了与动物们奇妙的缘分之旅。

---

## 八、开发与资源选型建议

### 1. 美术风格
- 主打Q版风格，后续可解锁写实风格皮肤或动物，满足不同玩家审美。
- 美术资源可用AI生成（如文心一格、通义万相、腾讯AI画匠），Q版和写实都支持，Q版更易批量生成和风格统一。

### 2. 游戏引擎
- 推荐Cocos Creator（TypeScript/JavaScript），支持微信小游戏、iOS、Android多端一键发布，适合主打微信小游戏并兼容App Store需求。

### 3. AI美术与资源平台
- 腾讯AI画匠、百度文心一格、阿里通义万相（国内可用，无需翻墙，支持Q版/写实多风格）。
- Canva可画（适合UI、icon）。
- 本地Stable Diffusion（如有算力，适合批量生成）。

### 4. AI大模型选型
- 文本生成：百度文心一言、阿里通义千问、腾讯混元（国内API，支持多语言，价格低，无需翻墙）。
- 视觉生成：同上AI美术平台。
- 多语言支持：上述大模型均支持中英，部分支持多语种，适合国际化需求。

### 5. 国际化方案
- 游戏文本、动物故事、道具说明等全部用国际化方案管理（如i18n）。
- 美术资源命名、UI设计等也要考虑多语言适配（如文本长度、字体兼容等）。

### 6. 广告与付费平衡建议
- 广告激励：每日观看广告次数有限，奖励基础道具、加速券、普通动物等，避免影响游戏节奏。
- 付费道具：提供高级道具、稀有动物、特殊皮肤等，付费获得更高价值或更快进度，但不影响核心体验。
- 碎片兑换：广告获得道具碎片，集齐可兑换稀有道具，既鼓励活跃又不强制氪金。
- 首充礼包/新手礼包：吸引首批付费，提升转化率。
- 价格建议：微信小游戏建议单价低、频次高（如6元、12元、18元档），App Store可适当拉高档位。

### 7. 其他建议
- 先用Q版风格快速上线，后续可根据用户反馈逐步引入写实风格。
- 多语言方案要在项目初期就设计好，避免后期大规模重构。
- 广告与付费平衡可通过A/B测试和数据分析不断优化。
- 关注微信小游戏平台的政策和审核要求，提前规避风险。

> 持续补充中…… 