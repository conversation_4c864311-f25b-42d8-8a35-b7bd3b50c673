import { _decorator, Component, sys } from 'cc';
import { AnimalInstance, AnimalStatus, InteractionType, InteractionResult } from '../data/AnimalInstance';
const { ccclass } = _decorator;

/**
 * 数据管理器
 * 负责游戏数据的存储、读取、管理
 */
@ccclass('DataManager')
export class DataManager {
    private static _instance: DataManager = null;
    
    // 游戏数据
    private _playerData: PlayerData = null;
    private _animalData: AnimalData[] = [];
    private _itemData: ItemData[] = [];
    
    public static get instance(): DataManager {
        if (this._instance === null) {
            this._instance = new DataManager();
        }
        return this._instance;
    }

    /**
     * 初始化数据管理器
     */
    public init() {
        this.loadPlayerData();
        this.loadAnimalData();
        this.loadItemData();
    }

    /**
     * 加载玩家数据
     */
    private loadPlayerData() {
        const savedData = sys.localStorage.getItem('playerData');
        if (savedData) {
            this._playerData = JSON.parse(savedData);
        } else {
            // 创建默认玩家数据
            this._playerData = this.createDefaultPlayerData();
            this.savePlayerData();
        }
    }

    /**
     * 创建默认玩家数据
     */
    private createDefaultPlayerData(): PlayerData {
        return {
            playerId: this.generateUUID(),
            playerName: "收容所管理员",
            level: 1,
            experience: 0,
            coins: 100,
            gems: 0,
            items: [],
            ownedAnimals: [],
            travelingAnimals: [],
            lastLoginTime: Date.now(),
            totalPlayTime: 0,
            achievements: []
        };
    }

    /**
     * 保存玩家数据
     */
    public savePlayerData() {
        sys.localStorage.setItem('playerData', JSON.stringify(this._playerData));
    }

    /**
     * 加载动物数据（Mock数据）
     */
    private loadAnimalData() {
        // 这里加载Mock的动物数据
        this._animalData = this.getMockAnimalData();
    }

    /**
     * 加载道具数据（Mock数据）
     */
    private loadItemData() {
        // 这里加载Mock的道具数据
        this._itemData = this.getMockItemData();
    }

    /**
     * 获取Mock动物数据
     */
    private getMockAnimalData(): AnimalData[] {
        return [
            // 猫类动物
            {
                id: "cat_001",
                name: "小橘",
                type: "cat",
                rarity: "common",
                description: "一只可爱的橘猫，喜欢晒太阳",
                iconPath: "textures/animals/cat_orange",
                personality: "活泼",
                favoriteFood: ["鱼", "猫薄荷"],
                specialAbility: "会抓老鼠"
            },
            {
                id: "cat_002",
                name: "雪球",
                type: "cat",
                rarity: "uncommon",
                description: "纯白色的波斯猫，优雅高贵",
                iconPath: "textures/animals/cat_white",
                personality: "优雅",
                favoriteFood: ["高级猫粮", "奶油"],
                specialAbility: "治愈心灵"
            },
            {
                id: "cat_003",
                name: "影子",
                type: "cat",
                rarity: "rare",
                description: "神秘的黑猫，据说能带来好运",
                iconPath: "textures/animals/cat_black",
                personality: "神秘",
                favoriteFood: ["夜宵", "月饼"],
                specialAbility: "夜视能力"
            },
            // 狗类动物
            {
                id: "dog_001",
                name: "小白",
                type: "dog",
                rarity: "common",
                description: "一只忠诚的小狗，最喜欢和主人玩耍",
                iconPath: "textures/animals/dog_white",
                personality: "忠诚",
                favoriteFood: ["骨头", "肉"],
                specialAbility: "看家护院"
            },
            {
                id: "dog_002",
                name: "金毛",
                type: "dog",
                rarity: "uncommon",
                description: "温顺的金毛犬，是最好的伙伴",
                iconPath: "textures/animals/dog_golden",
                personality: "温顺",
                favoriteFood: ["狗粮", "水果"],
                specialAbility: "导盲犬技能"
            },
            {
                id: "dog_003",
                name: "哈士奇",
                type: "dog",
                rarity: "rare",
                description: "精力充沛的哈士奇，喜欢拆家",
                iconPath: "textures/animals/dog_husky",
                personality: "调皮",
                favoriteFood: ["冰块", "肉干"],
                specialAbility: "雪橇犬"
            },
            // 兔类动物
            {
                id: "rabbit_001",
                name: "棉花糖",
                type: "rabbit",
                rarity: "common",
                description: "软萌的小白兔，像棉花糖一样可爱",
                iconPath: "textures/animals/rabbit_white",
                personality: "温柔",
                favoriteFood: ["胡萝卜", "青菜"],
                specialAbility: "跳跃高手"
            },
            {
                id: "rabbit_002",
                name: "巧克力",
                type: "rabbit",
                rarity: "uncommon",
                description: "棕色的垂耳兔，耳朵特别可爱",
                iconPath: "textures/animals/rabbit_brown",
                personality: "害羞",
                favoriteFood: ["苹果", "干草"],
                specialAbility: "挖洞专家"
            },
            // 鸟类动物
            {
                id: "bird_001",
                name: "小黄",
                type: "bird",
                rarity: "common",
                description: "活泼的小金丝雀，歌声动听",
                iconPath: "textures/animals/bird_canary",
                personality: "活泼",
                favoriteFood: ["鸟食", "水果"],
                specialAbility: "美妙歌声"
            },
            {
                id: "bird_002",
                name: "彩虹",
                type: "bird",
                rarity: "epic",
                description: "美丽的鹦鹉，会说很多话",
                iconPath: "textures/animals/bird_parrot",
                personality: "聪明",
                favoriteFood: ["坚果", "水果"],
                specialAbility: "模仿说话"
            },
            // 传说级动物
            {
                id: "dragon_001",
                name: "小龙",
                type: "dragon",
                rarity: "legendary",
                description: "传说中的小龙，拥有神奇的力量",
                iconPath: "textures/animals/dragon_baby",
                personality: "威严",
                favoriteFood: ["宝石", "魔法水晶"],
                specialAbility: "魔法力量"
            }
        ];
    }

    /**
     * 获取Mock道具数据
     */
    private getMockItemData(): ItemData[] {
        return [
            // 食物类道具
            {
                id: "food_fish",
                name: "小鱼干",
                type: "food",
                rarity: "common",
                description: "猫咪最爱的小鱼干，新鲜美味",
                iconPath: "textures/items/food/fish",
                price: 10,
                effect: "增加猫类动物好感度+10"
            },
            {
                id: "food_bone",
                name: "美味骨头",
                type: "food",
                rarity: "common",
                description: "狗狗最爱的大骨头",
                iconPath: "textures/items/food/bone",
                price: 12,
                effect: "增加狗类动物好感度+10"
            },
            {
                id: "food_carrot",
                name: "新鲜胡萝卜",
                type: "food",
                rarity: "common",
                description: "兔子最爱的胡萝卜",
                iconPath: "textures/items/food/carrot",
                price: 8,
                effect: "增加兔类动物好感度+10"
            },
            {
                id: "food_premium",
                name: "高级营养餐",
                type: "food",
                rarity: "rare",
                description: "营养丰富的高级食物，所有动物都喜欢",
                iconPath: "textures/items/food/premium",
                price: 50,
                effect: "增加所有动物好感度+20"
            },
            // 玩具类道具
            {
                id: "toy_ball",
                name: "毛线球",
                type: "toy",
                rarity: "common",
                description: "猫咪喜欢的毛线球玩具",
                iconPath: "textures/items/toys/ball",
                price: 15,
                effect: "增加动物快乐值+15"
            },
            {
                id: "toy_frisbee",
                name: "飞盘",
                type: "toy",
                rarity: "common",
                description: "狗狗最爱的飞盘游戏",
                iconPath: "textures/items/toys/frisbee",
                price: 20,
                effect: "增加狗类动物快乐值+20"
            },
            {
                id: "toy_tunnel",
                name: "隧道玩具",
                type: "toy",
                rarity: "uncommon",
                description: "小动物们喜欢的隧道玩具",
                iconPath: "textures/items/toys/tunnel",
                price: 35,
                effect: "增加小型动物快乐值+25"
            },
            // 工具类道具
            {
                id: "tool_brush",
                name: "梳毛刷",
                type: "tool",
                rarity: "common",
                description: "给动物梳毛的专用刷子",
                iconPath: "textures/items/tools/brush",
                price: 25,
                effect: "增加动物清洁度+30"
            },
            {
                id: "tool_camera",
                name: "拍立得相机",
                type: "tool",
                rarity: "rare",
                description: "可以和动物合影的相机",
                iconPath: "textures/items/tools/camera",
                price: 100,
                effect: "解锁合影功能"
            },
            {
                id: "tool_translator",
                name: "语音翻译器",
                type: "tool",
                rarity: "epic",
                description: "可以翻译动物语言的神奇道具",
                iconPath: "textures/items/tools/translator",
                price: 200,
                effect: "解锁AI对话功能"
            },
            // 召唤类道具
            {
                id: "scroll_basic",
                name: "基础通灵卷轴",
                type: "summon",
                rarity: "uncommon",
                description: "可以召唤动物的神秘卷轴",
                iconPath: "textures/items/scrolls/basic",
                price: 0,
                effect: "召唤随机动物来访"
            },
            {
                id: "scroll_premium",
                name: "高级通灵卷轴",
                type: "summon",
                rarity: "rare",
                description: "更强力的召唤卷轴，可召唤稀有动物",
                iconPath: "textures/items/scrolls/premium",
                price: 50,
                effect: "召唤稀有动物，概率提升10%"
            },
            // 装饰类道具
            {
                id: "deco_bed",
                name: "舒适小床",
                type: "decoration",
                rarity: "common",
                description: "给动物休息的舒适小床",
                iconPath: "textures/items/decoration/bed",
                price: 40,
                effect: "增加动物舒适度+20"
            },
            {
                id: "deco_tree",
                name: "猫爬架",
                type: "decoration",
                rarity: "uncommon",
                description: "猫咪专用的爬架玩具",
                iconPath: "textures/items/decoration/tree",
                price: 80,
                effect: "增加猫类动物快乐值+30"
            }
        ];
    }

    /**
     * 生成UUID
     */
    private generateUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // 数据操作方法

    /**
     * 添加金币
     */
    public addCoins(amount: number) {
        this._playerData.coins += amount;
        this.savePlayerData();
    }

    /**
     * 消费金币
     */
    public spendCoins(amount: number): boolean {
        if (this._playerData.coins >= amount) {
            this._playerData.coins -= amount;
            this.savePlayerData();
            return true;
        }
        return false;
    }

    /**
     * 添加宝石
     */
    public addGems(amount: number) {
        this._playerData.gems += amount;
        this.savePlayerData();
    }

    /**
     * 消费宝石
     */
    public spendGems(amount: number): boolean {
        if (this._playerData.gems >= amount) {
            this._playerData.gems -= amount;
            this.savePlayerData();
            return true;
        }
        return false;
    }

    /**
     * 收养动物
     */
    public adoptAnimal(animalId: string): boolean {
        if (!this._playerData.ownedAnimals.includes(animalId)) {
            this._playerData.ownedAnimals.push(animalId);
            this.savePlayerData();
            return true;
        }
        return false;
    }

    /**
     * 动物外出旅行
     */
    public animalStartTravel(animalId: string): boolean {
        const index = this._playerData.ownedAnimals.indexOf(animalId);
        if (index !== -1 && !this._playerData.travelingAnimals.includes(animalId)) {
            this._playerData.travelingAnimals.push(animalId);
            this.savePlayerData();
            return true;
        }
        return false;
    }

    /**
     * 动物旅行归来
     */
    public animalReturnFromTravel(animalId: string): boolean {
        const index = this._playerData.travelingAnimals.indexOf(animalId);
        if (index !== -1) {
            this._playerData.travelingAnimals.splice(index, 1);
            this.savePlayerData();
            return true;
        }
        return false;
    }

    /**
     * 添加道具
     */
    public addItem(itemId: string): boolean {
        this._playerData.items.push(itemId);
        this.savePlayerData();
        return true;
    }

    /**
     * 使用道具
     */
    public useItem(itemId: string): boolean {
        const index = this._playerData.items.indexOf(itemId);
        if (index !== -1) {
            this._playerData.items.splice(index, 1);
            this.savePlayerData();
            return true;
        }
        return false;
    }

    /**
     * 获取动物信息
     */
    public getAnimalById(animalId: string): AnimalData | null {
        return this._animalData.find(animal => animal.id === animalId) || null;
    }

    /**
     * 获取道具信息
     */
    public getItemById(itemId: string): ItemData | null {
        return this._itemData.find(item => item.id === itemId) || null;
    }

    /**
     * 获取在家的动物列表
     */
    public getHomeAnimals(): AnimalData[] {
        const homeAnimalIds = this._playerData.ownedAnimals.filter(
            id => !this._playerData.travelingAnimals.includes(id)
        );
        return homeAnimalIds.map(id => this.getAnimalById(id)).filter(animal => animal !== null);
    }

    /**
     * 获取旅行中的动物列表
     */
    public getTravelingAnimals(): AnimalData[] {
        return this._playerData.travelingAnimals.map(id => this.getAnimalById(id)).filter(animal => animal !== null);
    }

    /**
     * 获取背包中的道具列表
     */
    public getInventoryItems(): ItemData[] {
        return this._playerData.items.map(id => this.getItemById(id)).filter(item => item !== null);
    }

    /**
     * 检查是否拥有道具
     */
    public hasItem(itemId: string): boolean {
        return this._playerData.items.includes(itemId);
    }

    /**
     * 获取道具数量
     */
    public getItemCount(itemId: string): number {
        return this._playerData.items.filter(id => id === itemId).length;
    }

    // Getter方法
    public get playerData(): PlayerData { return this._playerData; }
    public get animalData(): AnimalData[] { return this._animalData; }
    public get itemData(): ItemData[] { return this._itemData; }
}

// 数据接口定义
export interface PlayerData {
    playerId: string;
    playerName: string;
    level: number;
    experience: number;
    coins: number;
    gems: number;
    items: string[];
    ownedAnimals: string[];
    travelingAnimals: string[];
    lastLoginTime: number;
    totalPlayTime: number;
    achievements: string[];
}

export interface AnimalData {
    id: string;
    name: string;
    type: string;
    rarity: string;
    description: string;
    iconPath: string;
    personality: string;
    favoriteFood: string[];
    specialAbility: string;
}

export interface ItemData {
    id: string;
    name: string;
    type: string;
    rarity: string;
    description: string;
    iconPath: string;
    price: number;
    effect: string;
}

// 动物交互相关方法扩展
declare module './DataManager' {
    interface DataManager {
        interactWithAnimal(instanceId: string, interactionType: InteractionType): InteractionResult;
        getAnimalInstance(instanceId: string): AnimalInstance;
        updateAnimalStatus(instanceId: string, status: AnimalStatus): void;
        addAnimalExperience(instanceId: string, exp: number): boolean;
    }
}

// 实现动物交互方法
DataManager.prototype.interactWithAnimal = function(instanceId: string, interactionType: InteractionType): InteractionResult {
    const animalInstance = this.getAnimalInstance(instanceId);
    if (!animalInstance) {
        return {
            success: false,
            message: "找不到指定的动物",
            effects: []
        };
    }

    // 检查动物状态
    if (animalInstance.status === AnimalStatus.TRAVELING || animalInstance.status === AnimalStatus.SLEEPING) {
        return {
            success: false,
            message: "动物当前无法互动",
            effects: []
        };
    }

    const result: InteractionResult = {
        success: true,
        message: "",
        effects: []
    };

    // 根据交互类型执行不同的逻辑
    switch (interactionType) {
        case InteractionType.FEED:
            this.handleFeedInteraction(animalInstance, result);
            break;
        case InteractionType.PLAY:
            this.handlePlayInteraction(animalInstance, result);
            break;
        case InteractionType.PET:
            this.handlePetInteraction(animalInstance, result);
            break;
        case InteractionType.TALK:
            this.handleTalkInteraction(animalInstance, result);
            break;
        default:
            result.success = false;
            result.message = "未知的交互类型";
            break;
    }

    if (result.success) {
        // 更新最后互动时间
        animalInstance.lastInteractTime = Date.now();
        animalInstance.totalInteractions++;

        // 保存数据
        this.savePlayerData();
    }

    return result;
};

DataManager.prototype.getAnimalInstance = function(instanceId: string): AnimalInstance {
    // 这里应该从玩家数据中查找动物实例
    // 暂时返回null，实际实现中需要在PlayerData中添加animalInstances数组
    console.log(`查找动物实例: ${instanceId}`);
    return null;
};

DataManager.prototype.updateAnimalStatus = function(instanceId: string, status: AnimalStatus): void {
    const animalInstance = this.getAnimalInstance(instanceId);
    if (animalInstance) {
        animalInstance.status = status;
        this.savePlayerData();
    }
};

DataManager.prototype.addAnimalExperience = function(instanceId: string, exp: number): boolean {
    const animalInstance = this.getAnimalInstance(instanceId);
    if (!animalInstance) return false;

    animalInstance.experience += exp;

    // 检查是否升级
    const expForNextLevel = this.getExpForLevel(animalInstance.level + 1);
    if (animalInstance.experience >= expForNextLevel) {
        animalInstance.level++;
        return true; // 返回true表示升级了
    }

    return false;
};

// 私有辅助方法
DataManager.prototype.handleFeedInteraction = function(animalInstance: AnimalInstance, result: InteractionResult): void {
    if (animalInstance.hunger >= 100) {
        result.success = false;
        result.message = "动物已经很饱了";
        return;
    }

    const hungerIncrease = 20 + Math.random() * 10;
    animalInstance.hunger = Math.min(100, animalInstance.hunger + hungerIncrease);
    animalInstance.happiness = Math.min(100, animalInstance.happiness + 5);

    result.message = "动物很开心地吃了食物";
    result.effects.push({ type: "hunger", value: hungerIncrease });
    result.effects.push({ type: "happiness", value: 5 });

    // 添加经验
    const levelUp = this.addAnimalExperience(animalInstance.instanceId, 10);
    if (levelUp) {
        result.levelUp = true;
        result.message += "，并且升级了！";
    }
};

DataManager.prototype.handlePlayInteraction = function(animalInstance: AnimalInstance, result: InteractionResult): void {
    if (animalInstance.happiness >= 100) {
        result.success = false;
        result.message = "动物已经很开心了";
        return;
    }

    const happinessIncrease = 15 + Math.random() * 10;
    animalInstance.happiness = Math.min(100, animalInstance.happiness + happinessIncrease);
    animalInstance.hunger = Math.max(0, animalInstance.hunger - 5);

    result.message = "和动物一起玩耍很开心";
    result.effects.push({ type: "happiness", value: happinessIncrease });
    result.effects.push({ type: "hunger", value: -5 });

    // 添加经验
    const levelUp = this.addAnimalExperience(animalInstance.instanceId, 15);
    if (levelUp) {
        result.levelUp = true;
        result.message += "，动物升级了！";
    }
};

DataManager.prototype.handlePetInteraction = function(animalInstance: AnimalInstance, result: InteractionResult): void {
    const happinessIncrease = 8 + Math.random() * 7;
    const friendshipIncrease = 3 + Math.random() * 5;

    animalInstance.happiness = Math.min(100, animalInstance.happiness + happinessIncrease);
    animalInstance.friendship = Math.min(100, animalInstance.friendship + friendshipIncrease);

    result.message = "动物很享受被抚摸";
    result.effects.push({ type: "happiness", value: happinessIncrease });
    result.effects.push({ type: "friendship", value: friendshipIncrease });

    // 添加经验
    const levelUp = this.addAnimalExperience(animalInstance.instanceId, 5);
    if (levelUp) {
        result.levelUp = true;
        result.message += "，动物升级了！";
    }
};

DataManager.prototype.handleTalkInteraction = function(animalInstance: AnimalInstance, result: InteractionResult): void {
    const friendshipIncrease = 5 + Math.random() * 8;

    animalInstance.friendship = Math.min(100, animalInstance.friendship + friendshipIncrease);

    const messages = [
        "动物似乎很喜欢和你聊天",
        "动物发出了开心的声音",
        "动物看起来很信任你",
        "你们之间的感情更深了"
    ];

    result.message = messages[Math.floor(Math.random() * messages.length)];
    result.effects.push({ type: "friendship", value: friendshipIncrease });

    // 添加经验
    const levelUp = this.addAnimalExperience(animalInstance.instanceId, 8);
    if (levelUp) {
        result.levelUp = true;
        result.message += "，动物升级了！";
    }
};

DataManager.prototype.getExpForLevel = function(level: number): number {
    return level * level * 100;
};
