/**
 * 游戏配置文件
 * 包含游戏的各种常量和配置参数
 */

export class GameConfig {
    // 游戏版本
    public static readonly VERSION = "1.0.0";
    
    // 屏幕配置
    public static readonly DESIGN_WIDTH = 750;
    public static readonly DESIGN_HEIGHT = 1334;
    
    // 动物配置
    public static readonly ANIMAL_CONFIG = {
        MAX_OWNED_ANIMALS: 20,           // 最大拥有动物数量
        MAX_TRAVELING_ANIMALS: 5,        // 最大旅行动物数量
        VISIT_INTERVAL_MIN: 30,          // 动物来访最小间隔（秒）
        VISIT_INTERVAL_MAX: 300,         // 动物来访最大间隔（秒）
        TRAVEL_DURATION_MIN: 3600,       // 旅行最短时间（秒）
        TRAVEL_DURATION_MAX: 86400,      // 旅行最长时间（秒）
    };
    
    // 道具配置
    public static readonly ITEM_CONFIG = {
        MAX_INVENTORY_SIZE: 100,         // 背包最大容量
        FOOD_EFFECT_DURATION: 1800,     // 食物效果持续时间（秒）
        TOY_EFFECT_DURATION: 3600,      // 玩具效果持续时间（秒）
    };
    
    // 经济配置
    public static readonly ECONOMY_CONFIG = {
        INITIAL_COINS: 100,              // 初始金币
        INITIAL_GEMS: 0,                 // 初始宝石
        DAILY_LOGIN_REWARD: 50,          // 每日登录奖励
        AD_REWARD_COINS: 20,             // 看广告奖励金币
        AD_REWARD_GEMS: 1,               // 看广告奖励宝石
    };
    
    // UI配置
    public static readonly UI_CONFIG = {
        ANIMATION_DURATION: 0.3,         // UI动画持续时间
        MESSAGE_DURATION: 2.0,           // 消息提示持续时间
        LOADING_MIN_DURATION: 1.0,       // 加载界面最小显示时间
    };
    
    // 音频配置
    public static readonly AUDIO_CONFIG = {
        BGM_VOLUME: 0.5,                 // 背景音乐音量
        SFX_VOLUME: 0.8,                 // 音效音量
        VOICE_VOLUME: 1.0,               // 语音音量
    };
    
    // 存储键名
    public static readonly STORAGE_KEYS = {
        PLAYER_DATA: "playerData",
        SETTINGS: "gameSettings",
        ACHIEVEMENTS: "achievements",
        STATISTICS: "gameStatistics",
    };
    
    // API配置（预留）
    public static readonly API_CONFIG = {
        BASE_URL: "https://api.myzoo.com",
        TIMEOUT: 10000,                  // 请求超时时间（毫秒）
        RETRY_COUNT: 3,                  // 重试次数
    };
    
    // 微信小游戏配置
    public static readonly WECHAT_CONFIG = {
        SHARE_TITLE: "我的动物收容所",
        SHARE_DESC: "快来和可爱的动物们一起玩耍吧！",
        SHARE_IMAGE: "textures/share/share_image.jpg",
    };
    
    // 动物稀有度配置
    public static readonly RARITY_CONFIG = {
        COMMON: { name: "普通", color: "#FFFFFF", probability: 0.6 },
        UNCOMMON: { name: "稀有", color: "#00FF00", probability: 0.25 },
        RARE: { name: "珍稀", color: "#0080FF", probability: 0.12 },
        EPIC: { name: "史诗", color: "#8000FF", probability: 0.025 },
        LEGENDARY: { name: "传说", color: "#FF8000", probability: 0.005 },
    };
    
    // 通灵卷轴配置
    public static readonly SUMMON_CONFIG = {
        BASIC_SCROLL: {
            name: "基础通灵卷轴",
            price: 0,                    // 通过广告获得
            cooldown: 3600,              // 冷却时间（秒）
            rareBonus: 0,                // 稀有度加成
        },
        PREMIUM_SCROLL: {
            name: "高级通灵卷轴",
            price: 50,                   // 宝石价格
            cooldown: 0,                 // 无冷却时间
            rareBonus: 0.1,              // 稀有度加成10%
        },
    };
    
    // 成就配置
    public static readonly ACHIEVEMENT_CONFIG = {
        FIRST_ANIMAL: { id: "first_animal", name: "第一个朋友", desc: "收养第一只动物" },
        ANIMAL_COLLECTOR: { id: "animal_collector", name: "动物收集家", desc: "收养10只不同的动物" },
        TRAVEL_MASTER: { id: "travel_master", name: "旅行大师", desc: "让动物外出旅行100次" },
        RICH_KEEPER: { id: "rich_keeper", name: "富有的管理员", desc: "拥有1000金币" },
    };
}

/**
 * 游戏事件常量
 */
export class GameEvents {
    // 动物相关事件
    public static readonly ANIMAL_VISIT = "animal_visit";           // 动物来访
    public static readonly ANIMAL_ADOPTED = "animal_adopted";       // 动物被收养
    public static readonly ANIMAL_TRAVEL = "animal_travel";         // 动物外出旅行
    public static readonly ANIMAL_RETURN = "animal_return";         // 动物旅行归来
    public static readonly ANIMAL_INTERACT = "animal_interact";     // 动物互动
    
    // UI相关事件
    public static readonly PANEL_OPEN = "panel_open";               // 面板打开
    public static readonly PANEL_CLOSE = "panel_close";             // 面板关闭
    public static readonly SCENE_CHANGE = "scene_change";           // 场景切换
    
    // 游戏相关事件
    public static readonly GAME_PAUSE = "game_pause";               // 游戏暂停
    public static readonly GAME_RESUME = "game_resume";             // 游戏恢复
    public static readonly LEVEL_UP = "level_up";                   // 等级提升
    public static readonly ACHIEVEMENT_UNLOCK = "achievement_unlock"; // 成就解锁
    
    // 经济相关事件
    public static readonly COINS_CHANGE = "coins_change";           // 金币变化
    public static readonly GEMS_CHANGE = "gems_change";             // 宝石变化
    public static readonly ITEM_PURCHASE = "item_purchase";         // 道具购买
    public static readonly AD_WATCHED = "ad_watched";               // 观看广告
}
