import { _decorator, Component, Node, Sprite, Label, Button, ProgressBar, tween, Vec3 } from 'cc';
import { AnimalData } from '../../data/AnimalData.ts';
import { AnimalInstance, AnimalStatus, InteractionType } from '../../data/AnimalInstance.ts';
import { DataManager } from '../../managers/DataManager.ts';
import { EventManager, GameEvents, AnimalEventData } from '../../managers/EventManager.ts';
const { ccclass, property } = _decorator;

/**
 * 动物显示组件
 * 用于显示动物的状态、属性和交互功能
 */
@ccclass('AnimalDisplay')
export class AnimalDisplay extends Component {
    
    @property({ type: Sprite, tooltip: "动物头像" })
    public animalAvatar: Sprite = null;
    
    @property({ type: Label, tooltip: "动物名称" })
    public animalNameLabel: Label = null;
    
    @property({ type: Label, tooltip: "动物等级" })
    public levelLabel: Label = null;
    
    @property({ type: ProgressBar, tooltip: "快乐值进度条" })
    public happinessBar: ProgressBar = null;
    
    @property({ type: ProgressBar, tooltip: "健康值进度条" })
    public healthBar: ProgressBar = null;
    
    @property({ type: ProgressBar, tooltip: "饥饿值进度条" })
    public hungerBar: ProgressBar = null;
    
    @property({ type: ProgressBar, tooltip: "经验值进度条" })
    public experienceBar: ProgressBar = null;
    
    @property({ type: Button, tooltip: "喂食按钮" })
    public feedButton: Button = null;
    
    @property({ type: Button, tooltip: "玩耍按钮" })
    public playButton: Button = null;
    
    @property({ type: Button, tooltip: "抚摸按钮" })
    public petButton: Button = null;
    
    @property({ type: Button, tooltip: "对话按钮" })
    public talkButton: Button = null;
    
    @property({ type: Node, tooltip: "状态图标容器" })
    public statusIconContainer: Node = null;
    
    @property({ type: Node, tooltip: "特性标签容器" })
    public traitsContainer: Node = null;

    private _animalInstance: AnimalInstance = null;
    private _animalData: AnimalData = null;
    private _isInteracting: boolean = false;

    onLoad() {
        this.setupButtons();
    }

    /**
     * 设置按钮事件
     */
    private setupButtons() {
        if (this.feedButton) {
            this.feedButton.node.on(Button.EventType.CLICK, () => this.interact(InteractionType.FEED), this);
        }
        
        if (this.playButton) {
            this.playButton.node.on(Button.EventType.CLICK, () => this.interact(InteractionType.PLAY), this);
        }
        
        if (this.petButton) {
            this.petButton.node.on(Button.EventType.CLICK, () => this.interact(InteractionType.PET), this);
        }
        
        if (this.talkButton) {
            this.talkButton.node.on(Button.EventType.CLICK, () => this.interact(InteractionType.TALK), this);
        }
    }

    /**
     * 设置动物数据
     */
    public setAnimalData(animalInstance: AnimalInstance) {
        this._animalInstance = animalInstance;
        this._animalData = DataManager.instance.getAnimalById(animalInstance.animalId);
        this.updateDisplay();
    }

    /**
     * 更新显示
     */
    private updateDisplay() {
        if (!this._animalInstance || !this._animalData) {
            this.clearDisplay();
            return;
        }
        
        this.updateBasicInfo();
        this.updateStatusBars();
        this.updateStatusIcons();
        this.updateTraits();
        this.updateButtons();
    }

    /**
     * 更新基本信息
     */
    private updateBasicInfo() {
        // 更新名称
        if (this.animalNameLabel) {
            const displayName = this._animalInstance.customName || this._animalData.name;
            this.animalNameLabel.string = displayName;
        }
        
        // 更新等级
        if (this.levelLabel) {
            this.levelLabel.string = `Lv.${this._animalInstance.level}`;
        }
        
        // 更新头像
        if (this.animalAvatar) {
            // 这里应该加载动物头像
            console.log(`加载动物头像: ${this._animalData.avatarPath}`);
        }
    }

    /**
     * 更新状态条
     */
    private updateStatusBars() {
        if (this.happinessBar) {
            this.happinessBar.progress = this._animalInstance.happiness / 100;
        }
        
        if (this.healthBar) {
            this.healthBar.progress = this._animalInstance.health / 100;
        }
        
        if (this.hungerBar) {
            this.hungerBar.progress = this._animalInstance.hunger / 100;
        }
        
        if (this.experienceBar) {
            const expToNext = this.getExpToNextLevel();
            const currentLevelExp = this._animalInstance.experience - this.getExpForLevel(this._animalInstance.level);
            this.experienceBar.progress = expToNext > 0 ? currentLevelExp / expToNext : 1;
        }
    }

    /**
     * 更新状态图标
     */
    private updateStatusIcons() {
        if (!this.statusIconContainer) return;
        
        // 清除现有图标
        this.statusIconContainer.removeAllChildren();
        
        // 根据动物状态添加图标
        switch (this._animalInstance.status) {
            case AnimalStatus.SLEEPING:
                this.addStatusIcon("sleep");
                break;
            case AnimalStatus.PLAYING:
                this.addStatusIcon("play");
                break;
            case AnimalStatus.EATING:
                this.addStatusIcon("eat");
                break;
            case AnimalStatus.SICK:
                this.addStatusIcon("sick");
                break;
            case AnimalStatus.TRAVELING:
                this.addStatusIcon("travel");
                break;
        }
    }

    /**
     * 添加状态图标
     */
    private addStatusIcon(iconType: string) {
        // 这里应该创建状态图标节点
        console.log(`添加状态图标: ${iconType}`);
    }

    /**
     * 更新特性标签
     */
    private updateTraits() {
        if (!this.traitsContainer) return;
        
        // 清除现有特性标签
        this.traitsContainer.removeAllChildren();
        
        // 添加特性标签
        this._animalInstance.traits.forEach(traitId => {
            this.addTraitLabel(traitId);
        });
    }

    /**
     * 添加特性标签
     */
    private addTraitLabel(traitId: string) {
        // 这里应该创建特性标签节点
        console.log(`添加特性标签: ${traitId}`);
    }

    /**
     * 更新按钮状态
     */
    private updateButtons() {
        const canInteract = !this._isInteracting && 
                           this._animalInstance.status !== AnimalStatus.TRAVELING &&
                           this._animalInstance.status !== AnimalStatus.SLEEPING;
        
        if (this.feedButton) {
            this.feedButton.interactable = canInteract && this._animalInstance.hunger < 100;
        }
        
        if (this.playButton) {
            this.playButton.interactable = canInteract && this._animalInstance.happiness < 100;
        }
        
        if (this.petButton) {
            this.petButton.interactable = canInteract;
        }
        
        if (this.talkButton) {
            this.talkButton.interactable = canInteract;
        }
    }

    /**
     * 执行交互
     */
    private interact(interactionType: InteractionType) {
        if (this._isInteracting || !this._animalInstance) return;
        
        this._isInteracting = true;
        
        // 播放交互动画
        this.playInteractionAnimation(interactionType);
        
        // 执行交互逻辑
        const result = DataManager.instance.interactWithAnimal(this._animalInstance.instanceId, interactionType);
        
        if (result) {
            // 发送交互事件
            const eventData: AnimalEventData = {
                animalId: this._animalInstance.animalId,
                instanceId: this._animalInstance.instanceId,
                animalData: this._animalInstance
            };
            
            EventManager.instance.emit(GameEvents.ANIMAL_INTERACT, eventData);
            
            // 更新显示
            this.scheduleOnce(() => {
                this.updateDisplay();
                this._isInteracting = false;
            }, 1);
        } else {
            this._isInteracting = false;
        }
    }

    /**
     * 播放交互动画
     */
    private playInteractionAnimation(interactionType: InteractionType) {
        if (!this.animalAvatar) return;
        
        // 简单的缩放动画
        const originalScale = this.animalAvatar.node.getScale();
        
        tween(this.animalAvatar.node)
            .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
            .to(0.1, { scale: originalScale })
            .start();
        
        console.log(`播放交互动画: ${interactionType}`);
    }

    /**
     * 获取升级所需经验
     */
    private getExpToNextLevel(): number {
        const nextLevel = this._animalInstance.level + 1;
        const currentLevelExp = this.getExpForLevel(this._animalInstance.level);
        const nextLevelExp = this.getExpForLevel(nextLevel);
        return nextLevelExp - currentLevelExp;
    }

    /**
     * 获取指定等级所需的总经验
     */
    private getExpForLevel(level: number): number {
        // 简单的经验计算公式
        return level * level * 100;
    }

    /**
     * 清除显示
     */
    private clearDisplay() {
        if (this.animalNameLabel) {
            this.animalNameLabel.string = "";
        }
        
        if (this.levelLabel) {
            this.levelLabel.string = "";
        }
        
        if (this.animalAvatar) {
            this.animalAvatar.spriteFrame = null;
        }
        
        // 重置进度条
        [this.happinessBar, this.healthBar, this.hungerBar, this.experienceBar].forEach(bar => {
            if (bar) bar.progress = 0;
        });
        
        // 清空容器
        if (this.statusIconContainer) {
            this.statusIconContainer.removeAllChildren();
        }
        
        if (this.traitsContainer) {
            this.traitsContainer.removeAllChildren();
        }
    }

    /**
     * 设置可见性
     */
    public setVisible(visible: boolean) {
        this.node.active = visible;
    }

    /**
     * 获取动物实例
     */
    public get animalInstance(): AnimalInstance {
        return this._animalInstance;
    }

    /**
     * 检查是否有动物
     */
    public hasAnimal(): boolean {
        return this._animalInstance !== null;
    }

    onDestroy() {
        // 清理按钮事件
        if (this.feedButton) {
            this.feedButton.node.off(Button.EventType.CLICK);
        }
        
        if (this.playButton) {
            this.playButton.node.off(Button.EventType.CLICK);
        }
        
        if (this.petButton) {
            this.petButton.node.off(Button.EventType.CLICK);
        }
        
        if (this.talkButton) {
            this.talkButton.node.off(Button.EventType.CLICK);
        }
    }
}
