import { _decorator, Component, Node, director, game, Game } from 'cc';
import { DataManager } from './managers/DataManager.ts';
import { UIManager } from './managers/UIManager.ts';
import { SceneManager } from './managers/SceneManager.ts';
const { ccclass, property } = _decorator;

/**
 * 游戏主管理器
 * 负责游戏的整体流程控制、场景管理、数据管理等核心功能
 */
@ccclass('GameManager')
export class GameManager extends Component {
    private static _instance: GameManager = null;
    
    @property({ type: Node, tooltip: "UI根节点" })
    public uiRoot: Node = null;
    
    @property({ type: Node, tooltip: "场景根节点" })
    public sceneRoot: Node = null;

    public static get instance(): GameManager {
        return this._instance;
    }

    onLoad() {
        if (GameManager._instance === null) {
            GameManager._instance = this;
            director.addPersistRootNode(this.node);
            this.initGame();
        } else {
            this.node.destroy();
        }
    }

    /**
     * 初始化游戏
     */
    private initGame() {
        console.log("游戏初始化开始");
        
        // 设置游戏为竖屏
        this.setPortraitMode();
        
        // 初始化各个管理器
        this.initManagers();
        
        // 加载游戏数据
        this.loadGameData();
        
        console.log("游戏初始化完成");
    }

    /**
     * 设置竖屏模式
     */
    private setPortraitMode() {
        // 设置游戏为竖屏模式
        game.frameRate = 60;
    }

    /**
     * 初始化各个管理器
     */
    private initManagers() {
        // 初始化数据管理器
        DataManager.instance.init();

        // 初始化UI管理器
        if (this.uiRoot) {
            UIManager.instance.init(this.uiRoot);
        }

        // 初始化场景管理器
        SceneManager.instance.init();

        console.log("所有管理器初始化完成");
    }

    /**
     * 加载游戏数据
     */
    private loadGameData() {
        // 加载本地存储的游戏数据
        // 如果没有则创建默认数据
    }

    /**
     * 保存游戏数据
     */
    public saveGameData() {
        // 保存游戏数据到本地存储
    }

    /**
     * 游戏暂停
     */
    public pauseGame() {
        game.pause();
    }

    /**
     * 游戏恢复
     */
    public resumeGame() {
        game.resume();
    }

    onDestroy() {
        if (GameManager._instance === this) {
            GameManager._instance = null;
        }
    }
}
