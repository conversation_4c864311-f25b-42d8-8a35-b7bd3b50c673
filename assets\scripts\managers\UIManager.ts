import { _decorator, Component, Node, Prefab, instantiate, Canvas, UITransform, Widget } from 'cc';
import { MessageBox, MessageType, MessageBoxData } from '../ui/components/MessageBox.ts';
import { LoadingPanel } from '../ui/components/LoadingPanel.ts';
import { ToastManager, ToastType } from '../ui/components/ToastMessage.ts';
const { ccclass } = _decorator;

/**
 * UI管理器
 * 负责UI界面的显示、隐藏、层级管理等
 */
@ccclass('UIManager')
export class UIManager {
    private static _instance: UIManager = null;
    
    private _uiRoot: Node = null;
    private _uiLayers: Map<UILayer, Node> = new Map();
    private _openPanels: Map<string, Node> = new Map();
    private _currentLoading: LoadingPanel = null;
    private _toastManager: ToastManager = null;
    
    public static get instance(): UIManager {
        if (this._instance === null) {
            this._instance = new UIManager();
        }
        return this._instance;
    }

    /**
     * 初始化UI管理器
     */
    public init(uiRoot: Node) {
        this._uiRoot = uiRoot;
        this.createUILayers();
        this.initToastManager();
    }

    /**
     * 创建UI层级
     */
    private createUILayers() {
        const layerNames = [
            { layer: UILayer.Background, name: "BackgroundLayer", zIndex: 0 },
            { layer: UILayer.Scene, name: "SceneLayer", zIndex: 100 },
            { layer: UILayer.UI, name: "UILayer", zIndex: 200 },
            { layer: UILayer.Popup, name: "PopupLayer", zIndex: 300 },
            { layer: UILayer.Loading, name: "LoadingLayer", zIndex: 400 },
            { layer: UILayer.Top, name: "TopLayer", zIndex: 500 }
        ];

        layerNames.forEach(({ layer, name, zIndex }) => {
            const layerNode = new Node(name);
            layerNode.setParent(this._uiRoot);
            
            // 添加Canvas组件
            const canvas = layerNode.addComponent(Canvas);
            canvas.cameraComponent = null; // 使用默认相机
            
            // 添加UITransform组件
            const uiTransform = layerNode.addComponent(UITransform);
            uiTransform.setContentSize(this._uiRoot.getComponent(UITransform).contentSize);
            
            // 添加Widget组件，使其充满父节点
            const widget = layerNode.addComponent(Widget);
            widget.isAlignTop = true;
            widget.isAlignBottom = true;
            widget.isAlignLeft = true;
            widget.isAlignRight = true;
            widget.top = 0;
            widget.bottom = 0;
            widget.left = 0;
            widget.right = 0;
            
            layerNode.setSiblingIndex(zIndex);
            this._uiLayers.set(layer, layerNode);
        });
    }

    /**
     * 初始化吐司管理器
     */
    private initToastManager() {
        this._toastManager = ToastManager.instance;
        const topLayer = this._uiLayers.get(UILayer.Top);
        if (topLayer) {
            this._toastManager.init(topLayer);
        }
    }

    /**
     * 显示UI面板
     */
    public showPanel(panelName: string, prefab: Prefab, layer: UILayer = UILayer.UI, data?: any): Node {
        // 如果面板已经打开，直接返回
        if (this._openPanels.has(panelName)) {
            const panel = this._openPanels.get(panelName);
            panel.active = true;
            return panel;
        }

        // 实例化面板
        const panelNode = instantiate(prefab);
        panelNode.name = panelName;
        
        // 添加到指定层级
        const layerNode = this._uiLayers.get(layer);
        if (layerNode) {
            panelNode.setParent(layerNode);
        }

        // 记录打开的面板
        this._openPanels.set(panelName, panelNode);

        // 如果面板有初始化方法，调用它
        const panelScript = panelNode.getComponent('BasePanel');
        if (panelScript && panelScript['init']) {
            panelScript['init'](data);
        }

        return panelNode;
    }

    /**
     * 隐藏UI面板
     */
    public hidePanel(panelName: string, destroy: boolean = false) {
        const panel = this._openPanels.get(panelName);
        if (panel) {
            if (destroy) {
                panel.destroy();
                this._openPanels.delete(panelName);
            } else {
                panel.active = false;
            }
        }
    }

    /**
     * 关闭所有面板
     */
    public closeAllPanels(excludePanels: string[] = []) {
        this._openPanels.forEach((panel, panelName) => {
            if (!excludePanels.includes(panelName)) {
                this.hidePanel(panelName, true);
            }
        });
    }

    /**
     * 获取面板
     */
    public getPanel(panelName: string): Node {
        return this._openPanels.get(panelName);
    }

    /**
     * 检查面板是否打开
     */
    public isPanelOpen(panelName: string): boolean {
        const panel = this._openPanels.get(panelName);
        return panel && panel.active;
    }

    /**
     * 显示吐司消息
     */
    public showToast(message: string, duration: number = 2, type: ToastType = ToastType.INFO) {
        if (this._toastManager) {
            this._toastManager.show(message, duration, type);
        } else {
            console.log(`吐司消息: ${message}`);
        }
    }

    /**
     * 显示消息提示
     */
    public showMessage(message: string, duration: number = 2) {
        this.showToast(message, duration, ToastType.INFO);
    }

    /**
     * 显示成功消息
     */
    public showSuccess(message: string, duration: number = 2) {
        this.showToast(message, duration, ToastType.SUCCESS);
    }

    /**
     * 显示警告消息
     */
    public showWarning(message: string, duration: number = 2) {
        this.showToast(message, duration, ToastType.WARNING);
    }

    /**
     * 显示错误消息
     */
    public showError(message: string, duration: number = 2) {
        this.showToast(message, duration, ToastType.ERROR);
    }

    /**
     * 显示消息框
     */
    public showMessageBox(data: MessageBoxData): Node {
        // 这里应该加载MessageBox预制体并显示
        console.log(`消息框: ${data.title || "提示"} - ${data.content}`);
        return null; // 实际实现中应该返回MessageBox节点
    }

    /**
     * 显示确认对话框
     */
    public showConfirm(title: string, content: string, onConfirm?: Function, onCancel?: Function): Node {
        const data: MessageBoxData = {
            type: MessageType.CONFIRM,
            title,
            content,
            onConfirm,
            onCancel
        };
        return this.showMessageBox(data);
    }

    /**
     * 显示信息对话框
     */
    public showInfo(title: string, content: string, onConfirm?: Function): Node {
        const data: MessageBoxData = {
            type: MessageType.INFO,
            title,
            content,
            onConfirm
        };
        return this.showMessageBox(data);
    }

    /**
     * 显示加载界面
     */
    public showLoading(message: string = "加载中...", showProgress: boolean = true): LoadingPanel {
        if (this._currentLoading) {
            this._currentLoading.setLoadingText(message);
            return this._currentLoading;
        }

        // 这里应该加载LoadingPanel预制体并显示
        console.log(`显示加载界面: ${message}`);
        return null; // 实际实现中应该返回LoadingPanel实例
    }

    /**
     * 更新加载进度
     */
    public updateLoadingProgress(progress: number, text?: string) {
        if (this._currentLoading) {
            this._currentLoading.setProgress(progress, text);
        }
    }

    /**
     * 隐藏加载界面
     */
    public hideLoading() {
        if (this._currentLoading) {
            this._currentLoading.close();
            this._currentLoading = null;
        }
    }
}

/**
 * UI层级枚举
 */
export enum UILayer {
    Background = 0,  // 背景层
    Scene = 1,       // 场景层
    UI = 2,          // UI层
    Popup = 3,       // 弹窗层
    Loading = 4,     // 加载层
    Top = 5          // 顶层
}
