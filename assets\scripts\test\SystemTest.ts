import { _decorator, Component, Node } from 'cc';
import { DataManager } from '../managers/DataManager.ts';
import { UIManager } from '../managers/UIManager.ts';
import { SceneManager } from '../managers/SceneManager.ts';
import { EventManager } from '../managers/EventManager.ts';
const { ccclass, property } = _decorator;

/**
 * 系统测试组件
 * 用于测试各个管理器和组件是否正常工作
 */
@ccclass('SystemTest')
export class SystemTest extends Component {

    onLoad() {
        console.log("=== 系统测试开始 ===");
        this.testManagers();
        this.testDataStructures();
        this.testEvents();
        console.log("=== 系统测试完成 ===");
    }

    /**
     * 测试管理器
     */
    private testManagers() {
        console.log("测试管理器...");
        
        // 测试DataManager
        const dataManager = DataManager.instance;
        console.log("DataManager实例:", dataManager ? "✓" : "✗");
        
        // 测试UIManager
        const uiManager = UIManager.instance;
        console.log("UIManager实例:", uiManager ? "✓" : "✗");
        
        // 测试SceneManager
        const sceneManager = SceneManager.instance;
        console.log("SceneManager实例:", sceneManager ? "✓" : "✗");
        
        // 测试EventManager
        const eventManager = EventManager.instance;
        console.log("EventManager实例:", eventManager ? "✓" : "✗");
    }

    /**
     * 测试数据结构
     */
    private testDataStructures() {
        console.log("测试数据结构...");
        
        const dataManager = DataManager.instance;
        
        // 测试动物数据
        const animalData = dataManager.getAnimalData();
        console.log("动物数据加载:", animalData.length > 0 ? `✓ (${animalData.length}种)` : "✗");
        
        // 测试道具数据
        const itemData = dataManager.getItemData();
        console.log("道具数据加载:", itemData.length > 0 ? `✓ (${itemData.length}种)` : "✗");
        
        // 测试玩家数据
        const playerData = dataManager.getPlayerData();
        console.log("玩家数据:", playerData ? "✓" : "✗");
        
        if (playerData) {
            console.log(`- 金币: ${playerData.coins}`);
            console.log(`- 钻石: ${playerData.diamonds}`);
            console.log(`- 背包物品: ${playerData.inventory.length}个`);
        }
    }

    /**
     * 测试事件系统
     */
    private testEvents() {
        console.log("测试事件系统...");
        
        const eventManager = EventManager.instance;
        let testEventReceived = false;
        
        // 注册测试事件
        const testCallback = () => {
            testEventReceived = true;
            console.log("测试事件接收: ✓");
        };
        
        eventManager.on("test_event", testCallback);
        
        // 发送测试事件
        eventManager.emit("test_event");
        
        // 检查结果
        console.log("事件系统:", testEventReceived ? "✓" : "✗");
        
        // 清理
        eventManager.off("test_event", testCallback);
    }

    /**
     * 测试UI组件
     */
    public testUIComponents() {
        console.log("测试UI组件...");
        
        const uiManager = UIManager.instance;
        
        // 测试Toast消息
        uiManager.showToast("这是一个测试消息");
        console.log("Toast消息: ✓");
        
        // 测试成功消息
        uiManager.showSuccess("测试成功消息");
        console.log("成功消息: ✓");
        
        // 测试警告消息
        uiManager.showWarning("测试警告消息");
        console.log("警告消息: ✓");
        
        // 测试错误消息
        uiManager.showError("测试错误消息");
        console.log("错误消息: ✓");
    }

    /**
     * 测试场景切换
     */
    public testSceneTransition() {
        console.log("测试场景切换...");
        
        const sceneManager = SceneManager.instance;
        
        // 获取当前场景
        const currentScene = sceneManager.getCurrentScene();
        console.log("当前场景:", currentScene || "未知");
        
        // 测试场景历史
        const history = sceneManager.getSceneHistory();
        console.log("场景历史:", history.length > 0 ? `✓ (${history.length}个)` : "✗");
    }

    /**
     * 测试数据保存和加载
     */
    public testDataPersistence() {
        console.log("测试数据持久化...");
        
        const dataManager = DataManager.instance;
        
        // 修改玩家数据
        const originalCoins = dataManager.getPlayerData().coins;
        dataManager.addCoins(100);
        
        // 保存数据
        dataManager.savePlayerData();
        console.log("数据保存: ✓");
        
        // 重新加载数据
        dataManager.loadPlayerData();
        const newCoins = dataManager.getPlayerData().coins;
        
        // 检查数据是否正确保存
        const dataPersisted = (newCoins === originalCoins + 100);
        console.log("数据持久化:", dataPersisted ? "✓" : "✗");
        
        if (dataPersisted) {
            console.log(`金币变化: ${originalCoins} -> ${newCoins}`);
        }
    }

    /**
     * 运行完整测试套件
     */
    public runFullTest() {
        console.log("=== 运行完整测试套件 ===");
        
        this.testManagers();
        this.testDataStructures();
        this.testEvents();
        
        // 延迟测试UI组件，避免同时显示太多消息
        this.scheduleOnce(() => {
            this.testUIComponents();
        }, 1);
        
        this.scheduleOnce(() => {
            this.testSceneTransition();
        }, 2);
        
        this.scheduleOnce(() => {
            this.testDataPersistence();
        }, 3);
        
        this.scheduleOnce(() => {
            console.log("=== 完整测试套件完成 ===");
        }, 4);
    }

    /**
     * 性能测试
     */
    public performanceTest() {
        console.log("=== 性能测试 ===");
        
        const iterations = 1000;
        
        // 测试事件系统性能
        const eventStart = performance.now();
        for (let i = 0; i < iterations; i++) {
            EventManager.instance.emit("perf_test", { data: i });
        }
        const eventTime = performance.now() - eventStart;
        console.log(`事件系统性能: ${iterations}次发送耗时 ${eventTime.toFixed(2)}ms`);
        
        // 测试数据访问性能
        const dataStart = performance.now();
        for (let i = 0; i < iterations; i++) {
            DataManager.instance.getPlayerData();
        }
        const dataTime = performance.now() - dataStart;
        console.log(`数据访问性能: ${iterations}次访问耗时 ${dataTime.toFixed(2)}ms`);
        
        console.log("=== 性能测试完成 ===");
    }
}
