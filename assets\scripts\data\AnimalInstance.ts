/**
 * 动物实例数据
 * 表示玩家拥有的具体动物实例，包含个性化数据
 */

export interface AnimalInstance {
    instanceId: string;          // 实例ID
    animalId: string;            // 动物模板ID
    customName?: string;         // 自定义名称
    level: number;               // 等级
    experience: number;          // 经验值
    happiness: number;           // 快乐值 (0-100)
    health: number;              // 健康值 (0-100)
    cleanliness: number;         // 清洁度 (0-100)
    hunger: number;              // 饥饿值 (0-100)
    friendship: number;          // 好感度 (0-100)
    adoptedTime: number;         // 收养时间戳
    lastInteractTime: number;    // 最后互动时间
    totalInteractions: number;   // 总互动次数
    status: AnimalStatus;        // 当前状态
    travelData?: TravelData;     // 旅行数据
    traits: string[];            // 特性列表
    memories: AnimalMemory[];    // 记忆列表
}

/**
 * 动物状态枚举
 */
export enum AnimalStatus {
    HOME = "home",               // 在家
    TRAVELING = "traveling",     // 旅行中
    VISITING = "visiting",       // 来访中（未收养）
    SLEEPING = "sleeping",       // 睡觉中
    PLAYING = "playing",         // 玩耍中
    EATING = "eating",           // 进食中
    SICK = "sick"                // 生病中
}

/**
 * 旅行数据
 */
export interface TravelData {
    startTime: number;           // 出发时间
    duration: number;            // 旅行时长（秒）
    destination: string;         // 目的地
    purpose: string;             // 旅行目的
    expectedReturn: number;      // 预计返回时间
    messages: TravelMessage[];   // 旅行消息列表
    gifts: TravelGift[];         // 带回的礼物
    photos: TravelPhoto[];       // 旅行照片
    earnings: number;            // 赚取的金币
}

/**
 * 旅行消息
 */
export interface TravelMessage {
    id: string;                  // 消息ID
    type: MessageType;           // 消息类型
    content: string;             // 消息内容
    timestamp: number;           // 发送时间
    isRead: boolean;             // 是否已读
    attachments?: MessageAttachment[]; // 附件
}

/**
 * 消息类型
 */
export enum MessageType {
    TEXT = "text",               // 文字消息
    PHOTO = "photo",             // 照片消息
    VOICE = "voice",             // 语音消息
    GIFT = "gift",               // 礼物消息
    LOCATION = "location"        // 位置消息
}

/**
 * 消息附件
 */
export interface MessageAttachment {
    type: string;                // 附件类型
    url: string;                 // 附件URL
    description?: string;        // 附件描述
}

/**
 * 旅行礼物
 */
export interface TravelGift {
    id: string;                  // 礼物ID
    name: string;                // 礼物名称
    description: string;         // 礼物描述
    iconPath: string;            // 图标路径
    value: number;               // 价值
    rarity: string;              // 稀有度
    story: string;               // 礼物故事
}

/**
 * 旅行照片
 */
export interface TravelPhoto {
    id: string;                  // 照片ID
    imagePath: string;           // 图片路径
    location: string;            // 拍摄地点
    timestamp: number;           // 拍摄时间
    description: string;         // 照片描述
    likes: number;               // 点赞数
}

/**
 * 动物记忆
 */
export interface AnimalMemory {
    id: string;                  // 记忆ID
    type: MemoryType;            // 记忆类型
    description: string;         // 记忆描述
    timestamp: number;           // 发生时间
    importance: number;          // 重要程度 (1-5)
    relatedItems?: string[];     // 相关道具
    emotion: string;             // 情感状态
}

/**
 * 记忆类型
 */
export enum MemoryType {
    FIRST_MEET = "first_meet",           // 初次见面
    ADOPTION = "adoption",               // 被收养
    FIRST_MEAL = "first_meal",           // 第一次进食
    PLAY_TIME = "play_time",             // 游戏时光
    TRAVEL = "travel",                   // 旅行经历
    GIFT_RECEIVED = "gift_received",     // 收到礼物
    SPECIAL_EVENT = "special_event",     // 特殊事件
    FRIENDSHIP_MILESTONE = "friendship_milestone" // 友谊里程碑
}

/**
 * 动物互动类型
 */
export enum InteractionType {
    FEED = "feed",               // 喂食
    PLAY = "play",               // 玩耍
    PET = "pet",                 // 抚摸
    CLEAN = "clean",             // 清洁
    TALK = "talk",               // 对话
    PHOTO = "photo",             // 拍照
    GIFT = "gift",               // 送礼物
    TRAIN = "train"              // 训练
}

/**
 * 互动结果
 */
export interface InteractionResult {
    success: boolean;            // 是否成功
    message: string;             // 结果消息
    effects: InteractionEffect[]; // 效果列表
    newMemory?: AnimalMemory;    // 新增记忆
    levelUp?: boolean;           // 是否升级
    newTrait?: string;           // 新获得特性
}

/**
 * 互动效果
 */
export interface InteractionEffect {
    type: string;                // 效果类型
    value: number;               // 效果数值
    duration?: number;           // 持续时间（秒）
}

/**
 * 动物特性配置
 */
export const AnimalTraits = {
    // 性格特性
    PLAYFUL: { id: "playful", name: "爱玩", description: "喜欢玩耍，快乐值增长更快" },
    SHY: { id: "shy", name: "害羞", description: "需要更多时间建立信任" },
    BRAVE: { id: "brave", name: "勇敢", description: "旅行时更容易获得稀有物品" },
    LAZY: { id: "lazy", name: "懒惰", description: "喜欢睡觉，体力恢复更快" },
    CURIOUS: { id: "curious", name: "好奇", description: "更容易发现新事物" },
    
    // 能力特性
    LUCKY: { id: "lucky", name: "幸运", description: "各种随机事件成功率提升" },
    SMART: { id: "smart", name: "聪明", description: "学习新技能更快" },
    STRONG: { id: "strong", name: "强壮", description: "健康值上限提升" },
    CHARMING: { id: "charming", name: "魅力", description: "更容易吸引其他动物" },
    LOYAL: { id: "loyal", name: "忠诚", description: "友谊值增长更快" }
};

/**
 * 旅行目的地配置
 */
export const TravelDestinations = {
    FOREST: { id: "forest", name: "神秘森林", description: "充满魔法的古老森林" },
    BEACH: { id: "beach", name: "阳光海滩", description: "温暖的海滨度假胜地" },
    MOUNTAIN: { id: "mountain", name: "雪山", description: "白雪皑皑的高山" },
    CITY: { id: "city", name: "繁华都市", description: "热闹的现代城市" },
    VILLAGE: { id: "village", name: "田园小镇", description: "宁静的乡村小镇" },
    DESERT: { id: "desert", name: "沙漠绿洲", description: "神秘的沙漠绿洲" },
    ISLAND: { id: "island", name: "热带岛屿", description: "美丽的热带天堂" },
    SPACE: { id: "space", name: "太空站", description: "未来的太空基地" }
};
