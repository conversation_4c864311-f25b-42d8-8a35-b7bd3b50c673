import { _decorator } from 'cc';
const { ccclass } = _decorator;

/**
 * 事件管理器
 * 负责游戏中各种事件的发布和订阅
 */
@ccclass('EventManager')
export class EventManager {
    private static _instance: EventManager = null;
    
    private _eventListeners: Map<string, Function[]> = new Map();
    
    public static get instance(): EventManager {
        if (this._instance === null) {
            this._instance = new EventManager();
        }
        return this._instance;
    }

    /**
     * 订阅事件
     */
    public on(eventName: string, callback: Function, target?: any) {
        if (!this._eventListeners.has(eventName)) {
            this._eventListeners.set(eventName, []);
        }
        
        const listeners = this._eventListeners.get(eventName);
        const boundCallback = target ? callback.bind(target) : callback;
        listeners.push(boundCallback);
    }

    /**
     * 取消订阅事件
     */
    public off(eventName: string, callback: Function, target?: any) {
        const listeners = this._eventListeners.get(eventName);
        if (listeners) {
            const boundCallback = target ? callback.bind(target) : callback;
            const index = listeners.indexOf(boundCallback);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 发布事件
     */
    public emit(eventName: string, ...args: any[]) {
        const listeners = this._eventListeners.get(eventName);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(`事件处理错误 [${eventName}]:`, error);
                }
            });
        }
    }

    /**
     * 一次性事件订阅
     */
    public once(eventName: string, callback: Function, target?: any) {
        const onceCallback = (...args: any[]) => {
            callback(...args);
            this.off(eventName, onceCallback);
        };
        this.on(eventName, onceCallback, target);
    }

    /**
     * 清除所有事件监听
     */
    public clear() {
        this._eventListeners.clear();
    }

    /**
     * 清除指定事件的所有监听
     */
    public clearEvent(eventName: string) {
        this._eventListeners.delete(eventName);
    }

    /**
     * 获取事件监听器数量
     */
    public getListenerCount(eventName: string): number {
        const listeners = this._eventListeners.get(eventName);
        return listeners ? listeners.length : 0;
    }
}

/**
 * 游戏事件常量
 */
export class GameEvents {
    // 动物相关事件
    public static readonly ANIMAL_VISIT = "animal_visit";           // 动物来访
    public static readonly ANIMAL_ADOPTED = "animal_adopted";       // 动物被收养
    public static readonly ANIMAL_TRAVEL_START = "animal_travel_start"; // 动物开始旅行
    public static readonly ANIMAL_TRAVEL_RETURN = "animal_travel_return"; // 动物旅行归来
    public static readonly ANIMAL_INTERACT = "animal_interact";     // 动物互动
    public static readonly ANIMAL_LEVEL_UP = "animal_level_up";     // 动物升级
    public static readonly ANIMAL_NEW_TRAIT = "animal_new_trait";   // 动物获得新特性
    public static readonly ANIMAL_STATUS_CHANGE = "animal_status_change"; // 动物状态变化
    
    // 旅行相关事件
    public static readonly TRAVEL_MESSAGE_RECEIVED = "travel_message_received"; // 收到旅行消息
    public static readonly TRAVEL_PHOTO_RECEIVED = "travel_photo_received";     // 收到旅行照片
    public static readonly TRAVEL_GIFT_RECEIVED = "travel_gift_received";       // 收到旅行礼物
    
    // UI相关事件
    public static readonly PANEL_OPEN = "panel_open";               // 面板打开
    public static readonly PANEL_CLOSE = "panel_close";             // 面板关闭
    public static readonly SCENE_CHANGE = "scene_change";           // 场景切换
    public static readonly UI_UPDATE = "ui_update";                 // UI更新
    
    // 游戏相关事件
    public static readonly GAME_PAUSE = "game_pause";               // 游戏暂停
    public static readonly GAME_RESUME = "game_resume";             // 游戏恢复
    public static readonly LEVEL_UP = "level_up";                   // 玩家等级提升
    public static readonly ACHIEVEMENT_UNLOCK = "achievement_unlock"; // 成就解锁
    public static readonly DAILY_REWARD = "daily_reward";           // 每日奖励
    
    // 经济相关事件
    public static readonly COINS_CHANGE = "coins_change";           // 金币变化
    public static readonly GEMS_CHANGE = "gems_change";             // 宝石变化
    public static readonly ITEM_PURCHASE = "item_purchase";         // 道具购买
    public static readonly ITEM_USE = "item_use";                   // 道具使用
    public static readonly AD_WATCHED = "ad_watched";               // 观看广告
    
    // 道具相关事件
    public static readonly ITEM_OBTAINED = "item_obtained";         // 获得道具
    public static readonly SUMMON_SCROLL_USE = "summon_scroll_use"; // 使用召唤卷轴
    public static readonly DRAWER_OPEN = "drawer_open";             // 抽屉打开
    public static readonly DRAWER_CLOSE = "drawer_close";           // 抽屉关闭
    
    // 社交相关事件
    public static readonly WECHAT_MESSAGE_SEND = "wechat_message_send"; // 发送微信消息
    public static readonly WECHAT_MESSAGE_RECEIVE = "wechat_message_receive"; // 接收微信消息
    public static readonly PHOTO_SHARE = "photo_share";             // 分享照片
    
    // 系统相关事件
    public static readonly SAVE_GAME = "save_game";                 // 保存游戏
    public static readonly LOAD_GAME = "load_game";                 // 加载游戏
    public static readonly SETTINGS_CHANGE = "settings_change";     // 设置变化
    public static readonly LANGUAGE_CHANGE = "language_change";     // 语言变化
}

/**
 * 事件数据接口
 */
export interface EventData {
    [key: string]: any;
}

/**
 * 动物事件数据
 */
export interface AnimalEventData extends EventData {
    animalId: string;
    instanceId?: string;
    animalData?: any;
}

/**
 * 经济事件数据
 */
export interface EconomyEventData extends EventData {
    type: 'coins' | 'gems';
    amount: number;
    reason: string;
    newTotal: number;
}

/**
 * UI事件数据
 */
export interface UIEventData extends EventData {
    panelName?: string;
    sceneName?: string;
    data?: any;
}

/**
 * 道具事件数据
 */
export interface ItemEventData extends EventData {
    itemId: string;
    itemData?: any;
    count?: number;
    reason?: string;
}

/**
 * 旅行事件数据
 */
export interface TravelEventData extends EventData {
    animalId: string;
    messageType?: string;
    content?: any;
    timestamp?: number;
}
