import { _decorator, Component, Node, Button } from 'cc';
import { UIManager } from '../managers/UIManager.ts';
import { SceneManager } from '../managers/SceneManager.ts';
import { DataManager } from '../managers/DataManager.ts';
import { EventManager, GameEvents } from '../managers/EventManager.ts';
const { ccclass, property } = _decorator;

/**
 * 场景基类
 * 所有场景都应该继承此类
 */
@ccclass('BaseScene')
export class BaseScene extends Component {
    
    @property({ type: Button, tooltip: "返回按钮" })
    public backButton: Button = null;
    
    @property({ type: Node, tooltip: "场景UI根节点" })
    public sceneUIRoot: Node = null;
    
    @property({ tooltip: "场景名称" })
    public sceneName: string = "";
    
    @property({ tooltip: "是否显示返回按钮" })
    public showBackButton: boolean = true;
    
    @property({ tooltip: "是否自动保存数据" })
    public autoSaveData: boolean = true;

    protected _isInitialized: boolean = false;
    protected _isActive: boolean = false;

    onLoad() {
        this.setupBackButton();
        this.registerEvents();
    }

    start() {
        this.initScene();
        this.onSceneStart();
    }

    /**
     * 设置返回按钮
     */
    private setupBackButton() {
        if (this.backButton) {
            this.backButton.node.active = this.showBackButton;
            this.backButton.node.on(Button.EventType.CLICK, this.onBackButtonClick, this);
        }
    }

    /**
     * 注册事件监听
     */
    private registerEvents() {
        EventManager.instance.on(GameEvents.GAME_PAUSE, this.onGamePause, this);
        EventManager.instance.on(GameEvents.GAME_RESUME, this.onGameResume, this);
        EventManager.instance.on(GameEvents.SCENE_CHANGE, this.onSceneChange, this);
    }

    /**
     * 初始化场景
     */
    private initScene() {
        if (this._isInitialized) return;
        
        console.log(`初始化场景: ${this.sceneName}`);
        
        // 发送场景切换事件
        EventManager.instance.emit(GameEvents.SCENE_CHANGE, {
            sceneName: this.sceneName,
            sceneNode: this.node
        });
        
        this._isInitialized = true;
        this._isActive = true;
    }

    /**
     * 场景开始时调用（子类重写）
     */
    protected onSceneStart() {
        // 子类重写此方法
    }

    /**
     * 场景激活时调用（子类重写）
     */
    protected onSceneActivate() {
        // 子类重写此方法
        this._isActive = true;
    }

    /**
     * 场景失活时调用（子类重写）
     */
    protected onSceneDeactivate() {
        // 子类重写此方法
        this._isActive = false;
        
        // 自动保存数据
        if (this.autoSaveData) {
            this.saveSceneData();
        }
    }

    /**
     * 场景销毁前调用（子类重写）
     */
    protected onSceneDestroy() {
        // 子类重写此方法
    }

    /**
     * 返回按钮点击事件
     */
    private onBackButtonClick() {
        this.goBack();
    }

    /**
     * 返回上一个场景
     */
    public goBack() {
        SceneManager.instance.goBack();
    }

    /**
     * 切换到指定场景
     */
    public gotoScene(sceneName: string) {
        SceneManager.instance.loadScene(sceneName);
    }

    /**
     * 显示加载界面
     */
    protected showLoading(message?: string) {
        UIManager.instance.showLoading(message);
    }

    /**
     * 隐藏加载界面
     */
    protected hideLoading() {
        UIManager.instance.hideLoading();
    }

    /**
     * 显示消息
     */
    protected showMessage(message: string, duration?: number) {
        UIManager.instance.showMessage(message, duration);
    }

    /**
     * 显示成功消息
     */
    protected showSuccess(message: string, duration?: number) {
        UIManager.instance.showSuccess(message, duration);
    }

    /**
     * 显示警告消息
     */
    protected showWarning(message: string, duration?: number) {
        UIManager.instance.showWarning(message, duration);
    }

    /**
     * 显示错误消息
     */
    protected showError(message: string, duration?: number) {
        UIManager.instance.showError(message, duration);
    }

    /**
     * 显示确认对话框
     */
    protected showConfirm(title: string, content: string, onConfirm?: Function, onCancel?: Function) {
        return UIManager.instance.showConfirm(title, content, onConfirm, onCancel);
    }

    /**
     * 保存场景数据
     */
    protected saveSceneData() {
        if (DataManager.instance) {
            DataManager.instance.savePlayerData();
        }
    }

    /**
     * 游戏暂停事件
     */
    private onGamePause() {
        this.onSceneDeactivate();
    }

    /**
     * 游戏恢复事件
     */
    private onGameResume() {
        this.onSceneActivate();
    }

    /**
     * 场景切换事件
     */
    private onSceneChange(data: any) {
        if (data.sceneName !== this.sceneName) {
            this.onSceneDeactivate();
        }
    }

    /**
     * 获取玩家数据
     */
    protected getPlayerData() {
        return DataManager.instance.playerData;
    }

    /**
     * 获取动物数据
     */
    protected getAnimalData() {
        return DataManager.instance.animalData;
    }

    /**
     * 获取道具数据
     */
    protected getItemData() {
        return DataManager.instance.itemData;
    }

    /**
     * 添加金币
     */
    protected addCoins(amount: number) {
        DataManager.instance.addCoins(amount);
        EventManager.instance.emit(GameEvents.COINS_CHANGE, {
            type: 'coins',
            amount: amount,
            reason: 'scene_reward',
            newTotal: this.getPlayerData().coins
        });
    }

    /**
     * 消费金币
     */
    protected spendCoins(amount: number): boolean {
        const success = DataManager.instance.spendCoins(amount);
        if (success) {
            EventManager.instance.emit(GameEvents.COINS_CHANGE, {
                type: 'coins',
                amount: -amount,
                reason: 'scene_purchase',
                newTotal: this.getPlayerData().coins
            });
        }
        return success;
    }

    /**
     * 添加宝石
     */
    protected addGems(amount: number) {
        DataManager.instance.addGems(amount);
        EventManager.instance.emit(GameEvents.GEMS_CHANGE, {
            type: 'gems',
            amount: amount,
            reason: 'scene_reward',
            newTotal: this.getPlayerData().gems
        });
    }

    /**
     * 检查场景是否激活
     */
    public get isActive(): boolean {
        return this._isActive;
    }

    /**
     * 检查场景是否已初始化
     */
    public get isInitialized(): boolean {
        return this._isInitialized;
    }

    onDestroy() {
        this.onSceneDestroy();
        
        // 清理事件监听
        EventManager.instance.off(GameEvents.GAME_PAUSE, this.onGamePause, this);
        EventManager.instance.off(GameEvents.GAME_RESUME, this.onGameResume, this);
        EventManager.instance.off(GameEvents.SCENE_CHANGE, this.onSceneChange, this);
        
        // 清理返回按钮事件
        if (this.backButton) {
            this.backButton.node.off(Button.EventType.CLICK, this.onBackButtonClick, this);
        }
    }
}
