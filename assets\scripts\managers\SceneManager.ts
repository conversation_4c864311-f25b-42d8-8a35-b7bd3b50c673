import { _decorator, Component, director, Scene } from 'cc';
const { ccclass } = _decorator;

/**
 * 场景管理器
 * 负责场景的切换、预加载等功能
 */
@ccclass('SceneManager')
export class SceneManager {
    private static _instance: SceneManager = null;
    
    private _currentScene: string = "";
    private _sceneHistory: string[] = [];
    
    public static get instance(): SceneManager {
        if (this._instance === null) {
            this._instance = new SceneManager();
        }
        return this._instance;
    }

    /**
     * 初始化场景管理器
     */
    public init() {
        this._currentScene = director.getScene().name;
    }

    /**
     * 切换场景
     */
    public loadScene(sceneName: string, onProgress?: (progress: number) => void): Promise<void> {
        return new Promise((resolve, reject) => {
            // 记录场景历史
            if (this._currentScene) {
                this._sceneHistory.push(this._currentScene);
            }

            director.loadScene(sceneName, (error: Error) => {
                if (error) {
                    console.error(`场景加载失败: ${sceneName}`, error);
                    reject(error);
                } else {
                    console.log(`场景加载成功: ${sceneName}`);
                    this._currentScene = sceneName;
                    resolve();
                }
            });
        });
    }

    /**
     * 预加载场景
     */
    public preloadScene(sceneName: string, onProgress?: (progress: number) => void): Promise<void> {
        return new Promise((resolve, reject) => {
            director.preloadScene(sceneName, (completedCount: number, totalCount: number, item: any) => {
                const progress = completedCount / totalCount;
                if (onProgress) {
                    onProgress(progress);
                }
            }, (error: Error) => {
                if (error) {
                    console.error(`场景预加载失败: ${sceneName}`, error);
                    reject(error);
                } else {
                    console.log(`场景预加载成功: ${sceneName}`);
                    resolve();
                }
            });
        });
    }

    /**
     * 返回上一个场景
     */
    public goBack(): Promise<void> {
        if (this._sceneHistory.length > 0) {
            const previousScene = this._sceneHistory.pop();
            return this.loadScene(previousScene);
        } else {
            console.warn("没有可返回的场景");
            return Promise.resolve();
        }
    }

    /**
     * 获取当前场景名称
     */
    public getCurrentScene(): string {
        return this._currentScene;
    }

    /**
     * 清空场景历史
     */
    public clearHistory() {
        this._sceneHistory = [];
    }
}

/**
 * 场景名称常量
 */
export const SceneNames = {
    MAIN: "MainScene",           // 主场景
    HOUSE: "HouseScene",         // 主屋场景
    BACKYARD: "BackyardScene",   // 后院场景
    SHOP: "ShopScene",           // 商店场景
    WECHAT: "WeChatScene"        // 微信场景
};
