import { _decorator, Component, Node, Label, tween, Vec3, UIOpacity } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 吐司消息组件
 * 用于显示临时的提示消息
 */
@ccclass('ToastMessage')
export class ToastMessage extends Component {
    
    @property({ type: Label, tooltip: "消息文本" })
    public messageLabel: Label = null;
    
    @property({ type: Node, tooltip: "背景节点" })
    public backgroundNode: Node = null;
    
    @property({ tooltip: "显示持续时间" })
    public duration: number = 2.0;
    
    @property({ tooltip: "淡入时间" })
    public fadeInTime: number = 0.3;
    
    @property({ tooltip: "淡出时间" })
    public fadeOutTime: number = 0.3;

    private _uiOpacity: UIOpacity = null;
    private _isShowing: boolean = false;

    onLoad() {
        this.setupComponents();
        this.node.active = false;
    }

    /**
     * 设置组件
     */
    private setupComponents() {
        // 添加透明度组件
        this._uiOpacity = this.node.getComponent(UIOpacity);
        if (!this._uiOpacity) {
            this._uiOpacity = this.node.addComponent(UIOpacity);
        }
        this._uiOpacity.opacity = 0;
    }

    /**
     * 显示消息
     */
    public showMessage(message: string, duration?: number) {
        if (this._isShowing) {
            // 如果正在显示，先隐藏再显示新消息
            this.hideImmediate();
        }
        
        this._isShowing = true;
        
        // 设置消息文本
        if (this.messageLabel) {
            this.messageLabel.string = message;
        }
        
        // 设置持续时间
        const showDuration = duration !== undefined ? duration : this.duration;
        
        // 显示节点
        this.node.active = true;
        
        // 播放显示动画
        this.playShowAnimation(showDuration);
    }

    /**
     * 播放显示动画
     */
    private playShowAnimation(duration: number) {
        // 初始状态
        this._uiOpacity.opacity = 0;
        this.node.setScale(0.8, 0.8, 1);
        
        // 淡入和缩放动画
        tween(this._uiOpacity)
            .to(this.fadeInTime, { opacity: 255 })
            .start();
            
        tween(this.node)
            .to(this.fadeInTime, { scale: new Vec3(1, 1, 1) }, { easing: 'backOut' })
            .delay(duration - this.fadeInTime - this.fadeOutTime)
            .call(() => {
                this.playHideAnimation();
            })
            .start();
    }

    /**
     * 播放隐藏动画
     */
    private playHideAnimation() {
        // 淡出动画
        tween(this._uiOpacity)
            .to(this.fadeOutTime, { opacity: 0 })
            .call(() => {
                this.hideComplete();
            })
            .start();
            
        // 向上移动动画
        const currentPos = this.node.getPosition();
        tween(this.node)
            .to(this.fadeOutTime, { 
                position: new Vec3(currentPos.x, currentPos.y + 50, currentPos.z) 
            })
            .start();
    }

    /**
     * 隐藏完成
     */
    private hideComplete() {
        this.node.active = false;
        this._isShowing = false;
        
        // 重置位置和缩放
        this.node.setScale(1, 1, 1);
        this.node.setPosition(0, 0, 0);
    }

    /**
     * 立即隐藏
     */
    public hideImmediate() {
        this.node.stopAllActions();
        this._uiOpacity.opacity = 0;
        this.hideComplete();
    }

    /**
     * 检查是否正在显示
     */
    public get isShowing(): boolean {
        return this._isShowing;
    }

    onDestroy() {
        this.node.stopAllActions();
    }
}

/**
 * 吐司消息管理器
 */
export class ToastManager {
    private static _instance: ToastManager = null;
    private _toastPool: ToastMessage[] = [];
    private _activeToasts: ToastMessage[] = [];
    private _toastParent: Node = null;

    public static get instance(): ToastManager {
        if (this._instance === null) {
            this._instance = new ToastManager();
        }
        return this._instance;
    }

    /**
     * 初始化吐司管理器
     */
    public init(parent: Node) {
        this._toastParent = parent;
    }

    /**
     * 显示吐司消息
     */
    public show(message: string, duration?: number, type?: ToastType) {
        const toast = this.getToast();
        if (toast) {
            // 设置样式（根据类型）
            this.setToastStyle(toast, type);
            
            // 显示消息
            toast.showMessage(message, duration);
            
            // 添加到活跃列表
            this._activeToasts.push(toast);
            
            // 设置完成回调
            this.scheduleToastComplete(toast);
        }
    }

    /**
     * 获取吐司实例
     */
    private getToast(): ToastMessage {
        // 从对象池获取或创建新的
        let toast = this._toastPool.pop();
        if (!toast) {
            toast = this.createToast();
        }
        return toast;
    }

    /**
     * 创建吐司实例
     */
    private createToast(): ToastMessage {
        // 这里应该创建吐司预制体实例
        // 暂时返回null，实际实现中需要加载预制体
        console.log("创建新的吐司实例");
        return null;
    }

    /**
     * 设置吐司样式
     */
    private setToastStyle(toast: ToastMessage, type?: ToastType) {
        // 根据类型设置不同的样式
        switch (type) {
            case ToastType.SUCCESS:
                // 设置成功样式（绿色背景等）
                break;
            case ToastType.WARNING:
                // 设置警告样式（黄色背景等）
                break;
            case ToastType.ERROR:
                // 设置错误样式（红色背景等）
                break;
            default:
                // 默认样式
                break;
        }
    }

    /**
     * 安排吐司完成回调
     */
    private scheduleToastComplete(toast: ToastMessage) {
        // 这里应该在吐司显示完成后将其回收到对象池
        setTimeout(() => {
            this.recycleToast(toast);
        }, (toast.duration + toast.fadeInTime + toast.fadeOutTime) * 1000);
    }

    /**
     * 回收吐司到对象池
     */
    private recycleToast(toast: ToastMessage) {
        const index = this._activeToasts.indexOf(toast);
        if (index !== -1) {
            this._activeToasts.splice(index, 1);
        }
        
        this._toastPool.push(toast);
    }

    /**
     * 清除所有吐司
     */
    public clearAll() {
        this._activeToasts.forEach(toast => {
            toast.hideImmediate();
        });
        this._activeToasts = [];
    }
}

/**
 * 吐司类型枚举
 */
export enum ToastType {
    INFO = "info",
    SUCCESS = "success",
    WARNING = "warning",
    ERROR = "error"
}
